

import React, { useContext, useState, useRef, useCallback, useEffect } from 'react';
import { UIText, Icons, AVAILABLE_GEMINI_MODELS, DefaultGameSettings, AVAILABLE_IMAGE_PROMPT_STYLES, <PERSON><PERSON><PERSON><PERSON>LE_SUMMARY_MODELS, GEMINI_MODEL_TEXT_FALLBACK } from '../constants';
import { ThemeContext } from '../contexts/ThemeContext';
import { Theme, GameSettingsData, CustomNarrativePrimaryElement, CustomNarrativeSubElement, AvailableModel, AvailableImagePromptStyle, SettingPreset, PresetType, UserPreferences, NotificationType, CharacterCardData, SillyTavernCharCard, SillyTavernCharCardEntry, SillyTavernEntry, RegexRule, RegexRuleScope } from '../types';
import { getAvailableProviders, getCurrentAIProvider } from '../services/aiService';
import { resetAIServiceManager } from '../services/geminiClient';
import { AIProviderType } from '../services/aiProviders/types';
import { NotificationContext } from '../contexts/NotificationContext';
import ConfirmationModal from './ConfirmationModal';
import { AIProviderSettingsSimple } from './AIProviderSettingsSimple';
// PNG parser and SillyTavern processing logic are moved to App.tsx

interface SettingsMenuProps {
  gameSettings: GameSettingsData;
  userPreferences: UserPreferences;
  onSettingsChange: (newSettings: Partial<GameSettingsData> | ((prevState: GameSettingsData) => GameSettingsData)) => void;
  onUserPreferencesChange: (newPreferences: UserPreferences) => void;
  characterCardPresets: SettingPreset[];
  userRolePresets: SettingPreset[];
  aiStylePresets: SettingPreset[];
  onSavePreset: (type: PresetType, value: string | CustomNarrativePrimaryElement[] | CharacterCardData, name: string) => void;
  onLoadPreset: (type: PresetType, presetId: string) => void;
  onDeletePreset: (type: PresetType, presetId: string) => void;
  onRequestConfirmation: (title: string, message: string, onConfirm: () => void, confirmText?: string, cancelText?: string) => void;
  enableBackdropBlur?: boolean;
}

interface PresetNameModalState {
  isOpen: boolean;
  presetType: PresetType | null;
  presetValue: string | CustomNarrativePrimaryElement[] | CharacterCardData | null;
  tempName: string;
  isUpdatingPresetId?: string;
}

// SillyTavern types are still needed if any display related to them remains, but processing is moved.
interface SillyTavernWorldBookFileStructure {
    entries: Record<string, SillyTavernEntry>;
}

type CharacterCardFieldKey = keyof CharacterCardData;
type SearchableSection = PresetType | 'worldBook';

// Simplified Pinyin converter (very limited for demo)
const pinyinMap: Record<string, { full: string, initial: string }> = {
  '角': { full: 'jue', initial: 'j' }, '色': { full: 'se', initial: 's' }, '卡': { full: 'ka', initial: 'k' },
  '我': { full: 'wo', initial: 'w' }, '的': { full: 'de', initial: 'd' },
  '文': { full: 'wen', initial: 'w' }, '风': { full: 'feng', initial: 'f' }, '格': { full: 'ge', initial: 'g'},
  '世': { full: 'shi', initial: 's' }, '界': { full: 'jie', initial: 'j' }, '书': { full: 'shu', initial: 's' },
  '元': { full: 'yuan', initial: 'y' }, '素': { full: 'su', initial: 's' }, '集': { full: 'ji', initial: 'j' },
  '名': { full: 'ming', initial: 'm' }, '称': { full: 'cheng', initial: 'c' },
  '规': { full: 'gui', initial: 'g' }, '则': { full: 'ze', initial: 'z' },
  '保': { full: 'bao', initial: 'b' }, '存': { full: 'cun', initial: 'c' },
  '预': { full: 'yu', initial: 'y' }, '设': { full: 'she', initial: 's' },
  '新': { full: 'xin', initial: 'x' }, '建': { full: 'jian', initial: 'j' },
  '默': { full: 'mo', initial: 'm' }, '认': { full: 'ren', initial: 'r' },
  '测': { full: 'ce', initial: 'c' }, '试': { full: 'shi', initial: 's' },
  '自定义': { full: 'zidingyi', initial: 'zdy' }, '导入': { full: 'daoru', initial: 'dr' },
  '默认': { full: 'moren', initial: 'mr' }, '系统': { full: 'xitong', initial: 'xt'},
  '玩家': { full: 'wanjia', initial: 'wj'}, '恐怖': { full: 'kongbu', initial: 'kb'},
  '氛围': { full: 'fenwei', initial: 'fw'}, '增强': { full: 'zengqiang', initial: 'zq'},
  '正则': { full: 'zhengze', initial: 'zz' }, '替换': { full: 'tihuan', initial: 'th' },
  '脚本': { full: 'jiaoben', initial: 'jb'}, '查找': { full: 'chazhao', initial: 'cz'},
  '修剪': { full: 'xiujian', initial: 'xj'}, '仅': { full: 'jin', initial: 'j'},
  '格式': { full: 'geshi', initial: 'gs'}, '显示': { full: 'xianshi', initial: 'xs'},
  '选项': { full: 'xuanxiang', initial: 'xx'},
};

const convertToPinyin = (text: string): { full: string, initials: string } => {
  let full = '';
  let initials = '';
  for (const char of text) {
    if (pinyinMap[char]) {
      full += pinyinMap[char].full;
      initials += pinyinMap[char].initial;
    } else if (/[a-zA-Z0-9\s]/.test(char)) {
      const lowerChar = char.toLowerCase();
      full += lowerChar;
      initials += lowerChar.trim() ? lowerChar[0] : '';
    }
  }
  return { full: full.replace(/\s+/g, ''), initials: initials.replace(/\s+/g, '') };
};

const itemMatchesSearchTerm = (itemName: string, searchTerm: string): boolean => {
  if (!searchTerm.trim()) return false;

  const lowerSearchTerm = searchTerm.toLowerCase().trim();

  if (itemName.toLowerCase().includes(lowerSearchTerm)) {
    return true;
  }

  if (/^[a-zA-Z0-9\s]*$/.test(searchTerm)) {
      const { full: pinyinFull, initials: pinyinInitials } = convertToPinyin(itemName);
      if (pinyinFull.includes(lowerSearchTerm) || pinyinInitials.includes(lowerSearchTerm)) {
        return true;
      }
  }
  return false;
};


interface SearchDropdownProps<T> {
  isOpen: boolean;
  items: T[];
  searchTerm: string;
  onSearchTermChange: (term: string) => void;
  onSelectItem: (item: T) => void;
  itemToString: (item: T) => string;
  itemToId: (item: T) => string;
  onClose: () => void;
  anchorRef: React.RefObject<HTMLButtonElement>;
  placeholderText?: string;
}

const SearchDropdown = <T extends unknown>({
  isOpen, items, searchTerm, onSearchTermChange, onSelectItem, itemToString, itemToId, onClose, anchorRef, placeholderText
}: SearchDropdownProps<T>) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 250 });

  const filteredItems = items.filter(item => itemMatchesSearchTerm(itemToString(item), searchTerm));

  useEffect(() => {
    if (isOpen && anchorRef.current) {
      const rect = anchorRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY + 2,
        left: rect.left + window.scrollX,
        width: Math.max(200, rect.width)
      });
    }
  }, [isOpen, anchorRef]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          anchorRef.current && !anchorRef.current.contains(event.target as Node)) {
        onClose();
      }
    };
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') onClose();
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, onClose, anchorRef]);

  if (!isOpen) return null;

  return (
    <div
      ref={dropdownRef}
      className="absolute z-50 mt-1 w-64 bg-secondary-themed rounded-md shadow-themed-lg border border-themed p-2 space-y-2"
      style={{ top: `${dropdownPosition.top}px`, left: `${dropdownPosition.left}px`, width: `${dropdownPosition.width}px` }}
    >
      <input
        type="text"
        value={searchTerm}
        onChange={(e) => onSearchTermChange(e.target.value)}
        placeholder={placeholderText || "搜索..."}
        className="w-full p-1.5 bg-element-themed text-primary-themed rounded-md border border-themed/70 focus:ring-1 focus:ring-accent-themed focus:border-transparent text-xs placeholder-themed"
        autoFocus
      />
      {filteredItems.length > 0 ? (
        <ul className="max-h-48 overflow-y-auto text-xs space-y-0.5">
          {filteredItems.map((item) => (
            <li key={itemToId(item)}>
              <button
                onClick={() => { onSelectItem(item); onClose(); }}
                className="w-full text-left p-1.5 rounded text-primary-themed hover:bg-element-themed transition-colors"
                title={itemToString(item)}
              >
                {itemToString(item)}
              </button>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-xs text-secondary-themed text-center py-1">{UIText.noSearchResults}</p>
      )}
    </div>
  );
};

interface PresetSectionProps {
  type: PresetType;
  presets: SettingPreset[];
  onLoadPreset: (type: PresetType, presetId: string) => void;
  onDeletePreset: (type: PresetType, presetId: string, presetName: string) => void;
  activePresetId?: string;
}

const PresetSection: React.FC<PresetSectionProps> = ({ type, presets, onLoadPreset, onDeletePreset, activePresetId }) => {
  const smallButtonClass = "p-1 text-xs btn-dreamy btn-dreamy-xs";
  
  const getPresetTypeName = (presetType: PresetType) => {
    switch(presetType) {
      case 'characterCard': return UIText.characterCardPresetLabel;
      case 'userRole': return UIText.userRolePreset;
      case 'systemRole': return UIText.systemRolePreset;
      default: return "预设";
    }
  };

  if (!presets || presets.length === 0) {
    return <p className="text-xs text-secondary-themed italic text-center py-1">尚无“{getPresetTypeName(type)}”预设。</p>;
  }

  return (
    <div className="space-y-1.5 max-h-40 overflow-y-auto pr-1 text-xs border-t border-b border-themed/10 py-1.5 my-1.5">
      {presets.map(preset => (
        <div 
          key={preset.id} 
          className={`flex justify-between items-center p-1 rounded-md transition-colors
          ${preset.id === activePresetId ? 'bg-accent-themed/20 border border-accent-themed/30' : 'bg-element-themed/30 hover:bg-element-themed/50 border border-transparent'}`}
        >
          <span 
            className="truncate flex-grow cursor-pointer hover:underline text-primary-themed"
            title={preset.name}
            onClick={() => onLoadPreset(type, preset.id)}
          >
            {preset.name}
          </span>
          <div className="flex space-x-1 flex-shrink-0 ml-1">
            <button onClick={() => onLoadPreset(type, preset.id)} className={smallButtonClass} title={`加载预设: ${preset.name}`} aria-label={`加载预设: ${preset.name}`}>
              <Icons.Load className="w-3 h-3" />
            </button>
            <button onClick={() => onDeletePreset(type, preset.id, preset.name)} className={smallButtonClass} title={`删除预设: ${preset.name}`} aria-label={`删除预设: ${preset.name}`}>
              <Icons.Trash className="w-3 h-3" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};


const SettingsMenu: React.FC<SettingsMenuProps> = ({
  gameSettings,
  userPreferences,
  onSettingsChange,
  onUserPreferencesChange,
  characterCardPresets,
  userRolePresets, aiStylePresets,
  onSavePreset, onLoadPreset, onDeletePreset, onRequestConfirmation,
  enableBackdropBlur = true,
}) => {
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext must be used within a ThemeProvider");
  const { theme, getThemeName, setTheme } = themeContext;

  const notificationContext = useContext(NotificationContext);
  if (!notificationContext) throw new Error("NotificationContext not found");
  const { addNotification } = notificationContext;

  const [newPrimaryElementName, setNewPrimaryElementName] = useState('');
  const [subElementInputs, setSubElementInputs] = useState<Record<string, { key: string, value: string }>>({});

  const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>(() => {
    const initialCollapseState: Record<string, boolean> = {
      display: false,
      aiProvidersRoot: true,
      userRoleRoot: true,
      systemRoleRoot: true,
      characterCardRoot: true,
      worldBookRoot: true,
      regexRoot: false,
      currentCharacterCardDetails: true,
      currentUserRoleDetails: true,
      currentSystemRoleDetails: true,
    };
    (gameSettings.customNarrativeElements || []).forEach(primaryEl => {
      initialCollapseState[primaryEl.id] = true; 
      (primaryEl.subElements || []).forEach(subEl => {
        initialCollapseState[subEl.id] = true; 
      });
    });
    (gameSettings.regexRules || []).forEach(regexRule => {
      initialCollapseState[`regex-${regexRule.id}`] = true; 
    });
    return initialCollapseState;
  });

  const [editingPrimaryId, setEditingPrimaryId] = useState<string | null>(null);
  const [tempPrimaryName, setTempPrimaryName] = useState<string>('');
  const [editingSubKeyId, setEditingSubKeyId] = useState<string | null>(null);
  const [editingSubValueId, setEditingSubValueId] = useState<string | null>(null);
  const [tempSubElementKey, setTempSubElementKey] = useState<string>('');
  const [tempSubElementValue, setTempSubElementValue] = useState<string>('');

  const subKeyInputRef = useRef<HTMLInputElement>(null);
  const subValueTextareaRef = useRef<HTMLTextAreaElement>(null);
  const presetNameInputRef = useRef<HTMLInputElement>(null);

  const [presetNameModalState, setPresetNameModalState] = useState<PresetNameModalState>({
    isOpen: false, presetType: null, presetValue: null, tempName: ''
  });

  const [activeSearchDropdown, setActiveSearchDropdown] = useState<SearchableSection | null>(null);
  const [currentSearchTerm, setCurrentSearchTerm] = useState<string>('');
  const searchButtonCharCardRef = useRef<HTMLButtonElement>(null);
  const searchButtonUserRoleRef = useRef<HTMLButtonElement>(null);
  const searchButtonSystemRoleRef = useRef<HTMLButtonElement>(null);
  const searchButtonWorldBookRef = useRef<HTMLButtonElement>(null);

  // AI Provider and Model states
  const [availableProviders, setAvailableProviders] = useState<Record<AIProviderType, { available: boolean; current: boolean }>>({});
  const [currentProvider, setCurrentProvider] = useState<AIProviderType | null>(null);
  const [availableModels, setAvailableModels] = useState<Record<AIProviderType, AvailableModel[]>>({});
  const [loadingModels, setLoadingModels] = useState<Record<AIProviderType, boolean>>({});
  const [modelsLastUpdated, setModelsLastUpdated] = useState<number>(0);

  const getSearchButtonRef = (type: SearchableSection) => {
    switch (type) {
      case 'characterCard': return searchButtonCharCardRef;
      case 'userRole': return searchButtonUserRoleRef;
      case 'systemRole': return searchButtonSystemRoleRef;
      case 'worldBook': return searchButtonWorldBookRef;
      default: return null;
    }
  };

  // Load AI providers and models
  const loadAIProviders = useCallback(async () => {
    try {
      console.log('Loading AI providers...');

      // Reset AI service manager to pick up new configuration
      resetAIServiceManager();

      const { providers, models } = await getAvailableProviders();
      console.log('Loaded providers:', providers);
      console.log('Loaded models:', models);

      setAvailableProviders(providers);

      const current = getCurrentAIProvider();
      console.log('Current provider:', current);
      setCurrentProvider(current);

      // Convert models to the expected format
      const modelsData: Record<AIProviderType, AvailableModel[]> = {} as Record<AIProviderType, AvailableModel[]>;
      Object.entries(models).forEach(([provider, modelList]) => {
        modelsData[provider as AIProviderType] = modelList.map(model => ({
          id: model.id || model.name || model,
          name: model.name || model.id || model,
          maxTokens: model.maxTokens
        }));
      });

      console.log('Converted models data:', modelsData);
      setAvailableModels(modelsData);
      setModelsLastUpdated(Date.now());
    } catch (error) {
      console.error('Failed to load AI providers:', error);
    }
  }, []);

  useEffect(() => {
    loadAIProviders();
  }, [loadAIProviders]);

  // Get all available models from all providers
  const getAllAvailableModels = useCallback((): AvailableModel[] => {
    console.log('getAllAvailableModels called');
    console.log('availableModels:', availableModels);
    console.log('availableProviders:', availableProviders);

    const allModels: AvailableModel[] = [];

    // Add models from all available providers
    Object.entries(availableModels).forEach(([provider, models]) => {
      console.log(`Processing provider ${provider}:`, models);
      console.log(`Provider ${provider} available:`, availableProviders[provider as AIProviderType]?.available);

      if (availableProviders[provider as AIProviderType]?.available && models && models.length > 0) {
        models.forEach(model => {
          allModels.push({
            id: model.id,
            name: `${model.name} (${provider})`,
            maxTokens: model.maxTokens
          });
        });
      }
    });

    // Always include Gemini models as fallback
    console.log('Adding Gemini models as fallback');
    AVAILABLE_GEMINI_MODELS.forEach(model => {
      allModels.push({
        id: model.id,
        name: model.name,
        maxTokens: model.maxTokens
      });
    });

    console.log('Final available models:', allModels);
    return allModels;
  }, [availableModels, availableProviders, modelsLastUpdated]);

  useEffect(() => {
    setCollapsedSections(prev => {
      const newCollapsedState = { ...prev };
      let stateChanged = false;
      (gameSettings.customNarrativeElements || []).forEach(primaryEl => {
        if (!(primaryEl.id in newCollapsedState)) {
          newCollapsedState[primaryEl.id] = true; 
          stateChanged = true;
        }
        (primaryEl.subElements || []).forEach(subEl => {
          if (!(subEl.id in newCollapsedState)) {
            newCollapsedState[subEl.id] = true; 
            stateChanged = true;
          }
        });
      });
      (gameSettings.regexRules || []).forEach(regexRule => {
        if (!(`regex-${regexRule.id}` in newCollapsedState)) {
          newCollapsedState[`regex-${regexRule.id}`] = true;
          stateChanged = true;
        }
      });
      
      const coreSections = ['display', 'aiProvidersRoot', 'userRoleRoot', 'systemRoleRoot', 'characterCardRoot', 'worldBookRoot', 'currentCharacterCardDetails', 'currentUserRoleDetails', 'currentSystemRoleDetails'];
      if (!('regexRoot' in newCollapsedState)) {
          newCollapsedState['regexRoot'] = false;
          stateChanged = true;
      }
      coreSections.forEach(key => {
        if (!(key in newCollapsedState)) {
          newCollapsedState[key] = true; 
          stateChanged = true;
        }
      });

      return stateChanged ? newCollapsedState : prev;
    });
  }, [gameSettings.customNarrativeElements, gameSettings.regexRules]);

  useEffect(() => { if (editingSubKeyId && subKeyInputRef.current) subKeyInputRef.current.focus(); }, [editingSubKeyId]);
  useEffect(() => { if (editingSubValueId && subValueTextareaRef.current) subValueTextareaRef.current.focus(); }, [editingSubValueId]);
  useEffect(() => { if (presetNameModalState.isOpen && presetNameInputRef.current) presetNameInputRef.current.focus(); }, [presetNameModalState.isOpen]);

  const handleGameSettingValueChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const settingName = name as keyof GameSettingsData;

    if (name === 'themeSelect') {
      setTheme(value as Theme);
      return;
    }

    let processedValue: any;

    if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    } else if (type === 'range' ||
               settingName === 'chatInterfaceOpacity' ||
               settingName === 'dialogueBubbleOpacity' ||
               settingName === 'dialogueBlur' ||
               settingName === 'minOutputChars' ||
               settingName === 'maxOutputChars' ||
               settingName === 'imageGenerationInterval') {
      processedValue = parseFloat(value);
      if (settingName === 'minOutputChars') {
        processedValue = Math.max(30, Math.min(1500, Number(processedValue) || DefaultGameSettings.minOutputChars));
      }
      if (settingName === 'maxOutputChars') {
        processedValue = Math.max(500, Math.min(5000, Number(processedValue) || DefaultGameSettings.maxOutputChars));
      }
    } else {
      processedValue = value;
    }

    if (settingName in DefaultGameSettings || settingName in gameSettings) {
        onSettingsChange({ [settingName]: processedValue } as Partial<GameSettingsData>);
    } else {
        console.warn(`Attempted to change an unknown setting: ${settingName}`);
    }
  };


  const handleCharacterCardValueChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    onSettingsChange(prev => ({
      ...prev,
      [name as keyof CharacterCardData]: value,
    }));
  };

  const handleFontSizeButtonClick = (direction: 'increase' | 'decrease') => {
    const currentScale = userPreferences.fontSizeScale;
    let newScale: number;

    if (direction === 'increase') {
      newScale = parseFloat((currentScale + 0.1).toFixed(1));
      newScale = Math.min(1.5, newScale);
    } else {
      newScale = parseFloat((currentScale - 0.1).toFixed(1));
      newScale = Math.max(0.8, newScale);
    }
    onUserPreferencesChange({ ...userPreferences, fontSizeScale: newScale });
  };


  const toggleSectionCollapse = (sectionId: string) => setCollapsedSections(prev => ({ ...prev, [sectionId]: !prev[sectionId] }));


  const handleAddPrimaryElement = () => {
    if (newPrimaryElementName.trim()) {
      const newPrimary: CustomNarrativePrimaryElement = { id: `primary_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`, name: newPrimaryElementName.trim(), isActive: true, subElements: [] };
      onSettingsChange(prev => ({ ...prev, customNarrativeElements: [...(prev.customNarrativeElements || []), newPrimary] }));
      setNewPrimaryElementName('');
    }
  };
  const handleDeletePrimaryElement = (primaryId: string) => {
    onSettingsChange(prev => ({ ...prev, customNarrativeElements: (prev.customNarrativeElements || []).filter(p => p.id !== primaryId) }));
    setCollapsedSections(prev => { const newState = { ...prev }; delete newState[primaryId]; return newState; });
  };

  const handleTogglePrimaryElementActive = (primaryId: string) => {
    onSettingsChange(prev => {
      const newCustomNarrativeElements = (prev.customNarrativeElements || []).map(p => {
        if (p.id === primaryId) {
          const subElements = p.subElements || [];
          const activeSubElementsCount = subElements.filter(sub => sub.isActive).length;
          const newSubElementsActiveState = !(subElements.length > 0 && activeSubElementsCount === subElements.length);
          
          let newPrimaryActiveState;
          if (subElements.length > 0) {
            newPrimaryActiveState = newSubElementsActiveState;
          } else {
            newPrimaryActiveState = !p.isActive;
          }
          
          return { ...p, isActive: newPrimaryActiveState, subElements: subElements.map(sub => ({ ...sub, isActive: newSubElementsActiveState })) };
        }
        return p;
      });
      return { ...prev, customNarrativeElements: newCustomNarrativeElements };
    });
  };


  const handleEditPrimaryName = (primaryEl: CustomNarrativePrimaryElement) => { setEditingPrimaryId(primaryEl.id); setTempPrimaryName(primaryEl.name); };
  const handleSavePrimaryNameEdit = () => { if (editingPrimaryId && tempPrimaryName.trim()) onSettingsChange(prev => ({ ...prev, customNarrativeElements: (prev.customNarrativeElements || []).map(p => p.id === editingPrimaryId ? { ...p, name: tempPrimaryName.trim() } : p) })); setEditingPrimaryId(null); setTempPrimaryName(''); };
  const handleCancelPrimaryNameEdit = () => { setEditingPrimaryId(null); setTempPrimaryName(''); };

  const handleSubElementInputChange = (primaryId: string, field: 'key' | 'value', inputValue: string) => setSubElementInputs(prev => ({ ...prev, [primaryId]: { ...(prev[primaryId] || { key: '', value: '' }), [field]: inputValue } }));
  const handleAddSubElement = (primaryId: string) => {
    const inputs = subElementInputs[primaryId] || { key: '', value: '' };
    if (inputs.key.trim()) {
      const newSubElement: CustomNarrativeSubElement = { id: `sub_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`, key: inputs.key.trim(), value: inputs.value.trim(), isActive: true };
      onSettingsChange(prev => ({ ...prev, customNarrativeElements: (prev.customNarrativeElements || []).map(p => p.id === primaryId ? { ...p, subElements: [...p.subElements, newSubElement], isActive: true } : p) }));
      setSubElementInputs(prev => ({ ...prev, [primaryId]: { key: '', value: '' } }));
      setCollapsedSections(prev => ({ ...prev, [newSubElement.id]: true })); 
    }
  };
  const handleDeleteSubElement = (primaryId: string, subId: string) => {
    onSettingsChange(prev => {
      const newCustomNarrativeElements = (prev.customNarrativeElements || []).map(p => {
        if (p.id === primaryId) {
          const updatedSubElements = p.subElements.filter(sub => sub.id !== subId);
          const newPrimaryActiveState = updatedSubElements.length > 0 ? updatedSubElements.some(sub => sub.isActive) : false;
          return { ...p, subElements: updatedSubElements, isActive: newPrimaryActiveState };
        }
        return p;
      });
      return { ...prev, customNarrativeElements: newCustomNarrativeElements };
    });
     setCollapsedSections(prev => { const newState = { ...prev }; delete newState[subId]; return newState; });
  };

  const handleToggleSubElementActive = (primaryId: string, subId: string) => {
    onSettingsChange(prev => {
      const newCustomNarrativeElements = (prev.customNarrativeElements || []).map(p => {
        if (p.id === primaryId) {
          const newSubElements = p.subElements.map(sub =>
            sub.id === subId ? { ...sub, isActive: !sub.isActive } : sub
          );
          const newActiveSubElementsCount = newSubElements.filter(sub => sub.isActive).length;
          let newPrimaryActiveState = p.isActive;
          if (p.subElements.length > 0) {
            newPrimaryActiveState = newActiveSubElementsCount > 0;
          }
          return { ...p, isActive: newPrimaryActiveState, subElements: newSubElements };
        }
        return p;
      });
      return { ...prev, customNarrativeElements: newCustomNarrativeElements };
    });
  };

  const handleEditSubKey = (subEl: CustomNarrativeSubElement) => { setEditingSubKeyId(subEl.id); setTempSubElementKey(subEl.key); setEditingSubValueId(null); };
  const handleSaveSubKey = (primaryId: string) => { if (editingSubKeyId && tempSubElementKey.trim()) onSettingsChange(prev => ({ ...prev, customNarrativeElements: (prev.customNarrativeElements || []).map(p => p.id === primaryId ? { ...p, subElements: p.subElements.map(sub => sub.id === editingSubKeyId ? { ...sub, key: tempSubElementKey.trim() } : sub) } : p) })); setEditingSubKeyId(null); setTempSubElementKey(''); };
  const handleCancelSubKeyEdit = () => { setEditingSubKeyId(null); setTempSubElementKey(''); };
  const handleEditSubValue = (subEl: CustomNarrativeSubElement) => { setEditingSubValueId(subEl.id); setTempSubElementValue(subEl.value); setEditingSubKeyId(null); };
  const handleSaveSubValue = (primaryId: string) => { if (editingSubValueId) onSettingsChange(prev => ({ ...prev, customNarrativeElements: (prev.customNarrativeElements || []).map(p => p.id === primaryId ? { ...p, subElements: p.subElements.map(sub => sub.id === editingSubValueId ? { ...sub, value: tempSubElementValue } : sub) } : p) })); setEditingSubValueId(null); setTempSubElementValue(''); };
  const handleCancelSubValueEdit = () => { setEditingSubValueId(null); setTempSubElementValue(''); };

  const handleOpenPresetNameModal = (type: PresetType, value: string | CustomNarrativePrimaryElement[] | CharacterCardData, isUpdatingPresetId?: string) => {
    const presetToUpdate = isUpdatingPresetId ? (
      type === 'characterCard' ? characterCardPresets :
      type === 'userRole' ? userRolePresets :
      type === 'systemRole' ? aiStylePresets :
      []
    ).find(p => p.id === isUpdatingPresetId) : undefined;

    setPresetNameModalState({
      isOpen: true,
      presetType: type,
      presetValue: value,
      tempName: presetToUpdate ? presetToUpdate.name : '',
      isUpdatingPresetId
    });
  };
  const handleClosePresetNameModal = () => setPresetNameModalState({ isOpen: false, presetType: null, presetValue: null, tempName: '' });

  const handleConfirmPresetName = () => {
    const { presetType, presetValue, tempName, isUpdatingPresetId } = presetNameModalState;
    if (presetType && presetValue !== null && tempName.trim()) {
      if ((presetType === 'characterCard' && typeof presetValue === 'object' && !Array.isArray(presetValue)) ||
          (typeof presetValue === 'string')
      ) {
        if (isUpdatingPresetId) {
            onDeletePreset(presetType, isUpdatingPresetId);
        }
        onSavePreset(presetType, presetValue as (string | CharacterCardData), tempName.trim());
        addNotification(UIText.presetSavedSuccess(tempName.trim()), "success");
      } else { addNotification("预设值类型错误。", "error"); }
    } else if (!tempName.trim()) { addNotification(UIText.presetNameInvalid, "error"); presetNameInputRef.current?.focus(); return; }
    handleClosePresetNameModal();
  };
  const handleDeletePresetClick = (type: PresetType, presetId: string, presetName: string) => onRequestConfirmation(`${UIText.deletePresetConfirm("").split("确定要删除预设 \"")[0]}${presetName}" 吗？`, UIText.deletePresetConfirm(presetName), () => { onDeletePreset(type, presetId); addNotification(UIText.presetDeletedSuccess(presetName), "success"); });


  const getCurrentCharacterCardData = useCallback((): CharacterCardData => ({
    characterName: gameSettings.characterName,
    characterDescription: gameSettings.characterDescription,
    characterOpeningMessage: gameSettings.characterOpeningMessage,
    characterPersonality: gameSettings.characterPersonality,
    characterScenario: gameSettings.characterScenario,
    characterExampleDialogue: gameSettings.characterExampleDialogue,
    characterPortraitKeywords: gameSettings.characterPortraitKeywords,
  }), [gameSettings]);

  const findActivePreset = useCallback((type: PresetType, currentValue: any, presets: SettingPreset[]): SettingPreset | undefined => {
    if (type === 'characterCard') {
      return presets.find(p => JSON.stringify(p.value) === JSON.stringify(currentValue));
    }
    return presets.find(p => p.value === currentValue);
  }, []);

  const toggleAllWorldBookElements = (enable: boolean) => {
    onSettingsChange(prev => ({
      ...prev,
      customNarrativeElements: (prev.customNarrativeElements || []).map(primaryEl => ({
        ...primaryEl,
        isActive: enable,
        subElements: primaryEl.subElements.map(subEl => ({
          ...subEl,
          isActive: enable,
        })),
      })),
    }));
    addNotification(`世界书所有规则已${enable ? '启用' : '禁用'}。`, "success");
  };

  const handleReorderWorldBook = () => {
    onSettingsChange(prev => {
      const currentElements = prev.customNarrativeElements || [];
      const activeElements = currentElements.filter(el => el.isActive);
      const inactiveElements = currentElements.filter(el => !el.isActive);
      const reorderedElements = [...activeElements, ...inactiveElements];
      addNotification("世界书已按勾选状态重新排序。", "success");
      return { ...prev, customNarrativeElements: reorderedElements };
    });
  };

  // --- Regex Replacement Logic ---
  const handleAddRegexRule = () => {
    const newRule: RegexRule = {
      id: `regex_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
      name: UIText.regexRuleNamePlaceholder.split(' ')[0] + ` ${ (gameSettings.regexRules || []).length + 1}`, 
      pattern: '',
      replacement: '',
      flags: 'gi',
      scope: 'all',
      isActive: true,
      isDisplayOnly: false, 
      trimInput: '',
    };
    onSettingsChange(prev => ({
      ...prev,
      regexRules: [...(prev.regexRules || []), newRule],
    }));
  };

  const handleRegexRuleChange = (ruleId: string, field: keyof RegexRule, value: string | boolean | RegexRuleScope) => {
    onSettingsChange(prev => ({
      ...prev,
      regexRules: (prev.regexRules || []).map(rule =>
        rule.id === ruleId ? { ...rule, [field]: value } : rule
      ),
    }));
  };

  const handleDeleteRegexRule = (ruleId: string) => {
    onSettingsChange(prev => ({
      ...prev,
      regexRules: (prev.regexRules || []).filter(rule => rule.id !== ruleId),
    }));
  };
  // --- End Regex Replacement Logic ---


  const inputClass = "w-full p-2 bg-element-themed text-primary-themed rounded-md border border-themed focus:ring-2 focus:ring-accent-themed focus:border-transparent text-sm placeholder-themed";
  const textareaClass = `${inputClass} min-h-[60px] resize-y`;
  const labelClass = "block text-sm font-medium text-primary-themed mb-1";
  const iconButtonClass = "p-1.5 text-secondary-themed hover:text-accent-themed rounded-md hover:bg-element-themed/50 transition-colors duration-150";
  const smallButtonClass = "p-1 text-xs btn-dreamy btn-dreamy-xs";

  const characterCardFields: Array<{ key: CharacterCardFieldKey, label: string, placeholder: string, type: 'input' | 'textarea', rows?: number }> = [
    { key: 'characterName', label: UIText.characterNameLabel, placeholder: UIText.characterNamePlaceholder, type: 'input' },
    { key: 'characterDescription', label: UIText.characterDescriptionLabel, placeholder: UIText.characterDescriptionPlaceholder, type: 'textarea', rows: 3 },
    { key: 'characterOpeningMessage', label: UIText.characterOpeningMessageLabel, placeholder: UIText.characterOpeningMessagePlaceholder, type: 'textarea', rows: 2 },
    { key: 'characterPersonality', label: UIText.characterPersonalityLabel, placeholder: UIText.characterPersonalityPlaceholder, type: 'textarea', rows: 2 },
    { key: 'characterScenario', label: UIText.characterScenarioLabel, placeholder: UIText.characterScenarioPlaceholder, type: 'textarea', rows: 2 },
    { key: 'characterExampleDialogue', label: UIText.characterExampleDialogueLabel, placeholder: UIText.characterExampleDialoguePlaceholder, type: 'textarea', rows: 3 },
    { key: 'characterPortraitKeywords', label: UIText.characterPortraitKeywordsLabel, placeholder: UIText.characterPortraitKeywordsPlaceholder, type: 'input' },
  ];

  const renderSectionWithToggle = (
    sectionId: string,
    title: string,
    children: React.ReactNode,
    headerActions?: React.ReactNode,
    IconComponent?: React.FC<React.SVGProps<SVGSVGElement>>,
    searchButtonRef?: React.RefObject<HTMLButtonElement>,
    onSearchClick?: () => void,
    onRefreshClick?: () => void,
    activePresetName?: string 
  ) => (
    <div className="p-2.5 bg-element-themed/30 rounded-lg border border-themed/20">
        <div className="flex justify-between items-center">
            <button onClick={() => toggleSectionCollapse(sectionId)} className="flex-grow flex items-center text-left text-primary-themed font-medium text-sm py-1 pr-2">
                {IconComponent && <IconComponent className="w-4 h-4 mr-2 text-accent-themed" />}
                {title}
                {activePresetName && (
                    <span 
                      className="ml-2 px-1.5 py-0.5 text-xs font-medium bg-accent-themed/20 text-accent-themed rounded-md inline-block align-middle truncate max-w-[100px] sm:max-w-[120px] md:max-w-[150px]" 
                      title={activePresetName}
                    >
                        {activePresetName}
                    </span>
                )}
            </button>
            <div className="flex items-center flex-shrink-0 ml-2 space-x-1">
                {headerActions}
                {onRefreshClick && (
                    <button onClick={onRefreshClick} className={iconButtonClass} title={UIText.reorderWorldBookByActiveTooltip}>
                        <Icons.ArrowPath className="w-4 h-4" />
                    </button>
                )}
                {onSearchClick && searchButtonRef && (
                    <button ref={searchButtonRef} onClick={onSearchClick} className={iconButtonClass} title={UIText.searchWorldBook}>
                        <Icons.Search className="w-4 h-4" />
                    </button>
                )}
                <button onClick={() => toggleSectionCollapse(sectionId)} className={`${iconButtonClass} p-1`}>
                    <Icons.ChevronDown className={`w-5 h-5 transform transition-transform text-secondary-themed ${collapsedSections[sectionId] ? 'rotate-[-90deg]' : ''}`} />
                </button>
            </div>
        </div>
        {!collapsedSections[sectionId] && <div className="mt-2 space-y-3 animate-fadeIn">{children}</div>}
    </div>
  );

  const renderCollapsibleDetails = (
    detailsId: string,
    title: string,
    activePreset: SettingPreset | undefined,
    onSaveAsNew: () => void,
    children: React.ReactNode,
    onUpdateExisting?: () => void,
  ) => (
    <div className="p-1.5 bg-element-themed/10 rounded-md border border-themed/10">
      <button
        onClick={() => toggleSectionCollapse(detailsId)}
        className="w-full flex justify-between items-center text-left text-primary-themed text-xs font-medium py-1 hover:bg-element-themed/20 rounded-sm px-1"
      >
        <span className="flex items-center">
          <Icons.ChevronDown className={`w-3.5 h-3.5 mr-1.5 transform transition-transform text-secondary-themed/80 ${collapsedSections[detailsId] ? 'rotate-[-90deg]' : ''}`} />
          {title} {activePreset && <span className="ml-1.5 text-xs text-accent-themed/80">(当前: {activePreset.name})</span>}
        </span>
        <div className="flex items-center space-x-1">
            {activePreset && onUpdateExisting && (
                 <button onClick={(e) => { e.stopPropagation(); onUpdateExisting(); }} className={smallButtonClass} title={`更新预设: ${activePreset.name}`} aria-label={`更新预设: ${activePreset.name}`}>
                    <Icons.ArrowPath className="w-3 h-3" />
                 </button>
            )}
            <button onClick={(e) => { e.stopPropagation(); onSaveAsNew(); }} className={smallButtonClass} title="另存为新预设" aria-label="另存为新预设">
                <Icons.Save className="w-3 h-3" />
            </button>
        </div>
      </button>
      {!collapsedSections[detailsId] && <div className="mt-1.5 pl-2 border-l-2 border-themed/20 animate-fadeIn space-y-2">{children}</div>}
    </div>
  );

  const activeCharCardPreset = findActivePreset('characterCard', getCurrentCharacterCardData(), characterCardPresets);
  const activeUserRolePreset = findActivePreset('userRole', gameSettings.userRole, userRolePresets);
  const activeSystemRolePreset = findActivePreset('systemRole', gameSettings.systemRole, aiStylePresets);
  
  // For PresetSection activePresetId prop
  const activeCharCardPresetId = activeCharCardPreset?.id;
  const activeUserRolePresetId = activeUserRolePreset?.id;
  const activeSystemRolePresetId = activeSystemRolePreset?.id;


  const handleSearchItemSelect = (item: SettingPreset | CustomNarrativePrimaryElement) => {
    if ('type' in item && (item.type === 'characterCard' || item.type === 'userRole' || item.type === 'systemRole')) {
      onLoadPreset(item.type, item.id);
      addNotification(UIText.presetLoadedSuccess(item.name), "success");
    } else if ('subElements' in item) {
        const worldBookItem = item as CustomNarrativePrimaryElement;
        setCollapsedSections(prev => ({ ...prev, worldBookRoot: false, [worldBookItem.id]: false }));
        setTimeout(() => {
            const element = document.getElementById(`worldbook-item-${worldBookItem.id}`);
            element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);
    }
    setActiveSearchDropdown(null);
    setCurrentSearchTerm('');
  };

  const openSearchDropdown = (type: SearchableSection) => {
    setActiveSearchDropdown(prev => prev === type ? null : type);
    setCurrentSearchTerm('');
  };

  return (
    <div className="space-y-4 text-sm">
      {renderSectionWithToggle('display', `${UIText.settings} (显示与模型)`, (
        <>
          <div>
            <label htmlFor="themeSelect" className={labelClass}>{UIText.themeToggle}</label>
            <select id="themeSelect" name="themeSelect" value={theme} onChange={handleGameSettingValueChange} className={`${inputClass} appearance-none mt-0.5`} aria-label={UIText.themeToggle}>
              {Object.values(Theme).map(themeOption => (<option key={themeOption} value={themeOption}>{getThemeName(themeOption)}</option>))}
            </select>
          </div>
          <div>
            <div className="flex items-center justify-between">
              <label htmlFor="selectedModelId" className={labelClass}>{UIText.modelSelectionLabel}</label>
              <button
                onClick={loadAIProviders}
                className="text-xs px-2 py-1 bg-accent-themed/10 hover:bg-accent-themed/20 text-accent-themed rounded transition-colors"
                title="重新加载AI提供商和模型"
              >
                🔄 刷新
              </button>
            </div>
            <select
              id="selectedModelId"
              name="selectedModelId"
              value={gameSettings.selectedModelId}
              onChange={handleGameSettingValueChange}
              className={inputClass}
            >
              {getAllAvailableModels().map(model => (
                <option key={model.id} value={model.id}>{model.name}</option>
              ))}
            </select>
            {Object.keys(loadingModels).some(provider => loadingModels[provider as AIProviderType]) && (
              <p className="text-xs text-secondary-themed mt-1">正在加载模型列表...</p>
            )}
            <div className="text-xs text-secondary-themed mt-1 space-y-1">
              <div>当前可用模型数量: {getAllAvailableModels().length}</div>
              <div>可用提供商: {Object.keys(availableProviders).filter(p => availableProviders[p as AIProviderType]?.available).join(', ') || '无'}</div>
              <div>当前提供商: {currentProvider || '未设置'}</div>
            </div>
          </div>
          <div>
            <label htmlFor="selectedSummaryModelId" className={labelClass}>{UIText.summaryModelSelectionLabel}</label>
            <select
              id="selectedSummaryModelId"
              name="selectedSummaryModelId"
              value={gameSettings.selectedSummaryModelId}
              onChange={handleGameSettingValueChange}
              className={inputClass}
            >
              {getAllAvailableModels().map(model => (
                <option key={model.id} value={model.id}>{model.name}</option>
              ))}
            </select>
            {Object.keys(loadingModels).some(provider => loadingModels[provider as AIProviderType]) && (
              <p className="text-xs text-secondary-themed mt-1">正在加载模型列表...</p>
            )}
          </div>
          
          <div>
            <label htmlFor="fontSizeScaleDecrease" className={labelClass}>
              {UIText.fontSizeScaleLabel}
            </label>
            <div className="flex items-center space-x-2 mt-1">
              <button
                id="fontSizeScaleDecrease"
                onClick={() => handleFontSizeButtonClick('decrease')}
                className="btn-dreamy btn-dreamy-xs p-1.5"
                aria-label="减少字号"
                disabled={userPreferences.fontSizeScale <= 0.8}
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-3.5 h-3.5"><path strokeLinecap="round" strokeLinejoin="round" d="M19.5 12h-15" /></svg>
              </button>
              <span className="text-sm text-primary-themed w-12 text-center tabular-nums">
                {userPreferences.fontSizeScale.toFixed(1)}x
              </span>
              <button
                id="fontSizeScaleIncrease"
                onClick={() => handleFontSizeButtonClick('increase')}
                className="btn-dreamy btn-dreamy-xs p-1.5"
                aria-label="增大字号"
                disabled={userPreferences.fontSizeScale >= 1.5}
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-3.5 h-3.5"><path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" /></svg>
              </button>
            </div>
          </div>

          <div><label htmlFor="chatInterfaceOpacity" className={labelClass}>{UIText.chatInterfaceOpacityLabel} ({gameSettings.chatInterfaceOpacity.toFixed(2)})</label><input type="range" id="chatInterfaceOpacity" name="chatInterfaceOpacity" min="0.1" max="1" step="0.01" value={gameSettings.chatInterfaceOpacity} onChange={handleGameSettingValueChange} className="w-full h-2 bg-element-themed rounded-lg appearance-none cursor-pointer accent-accent-themed" /></div>
          <div><label htmlFor="dialogueBubbleOpacity" className={labelClass}>{UIText.dialogueBubbleOpacityLabel} ({gameSettings.dialogueBubbleOpacity.toFixed(2)})</label><input type="range" id="dialogueBubbleOpacity" name="dialogueBubbleOpacity" min="0.1" max="1" step="0.01" value={gameSettings.dialogueBubbleOpacity} onChange={handleGameSettingValueChange} className="w-full h-2 bg-element-themed rounded-lg appearance-none cursor-pointer accent-accent-themed" /></div>
          <div><label htmlFor="dialogueBlur" className={labelClass}>{UIText.dialogueBlurLabel} ({gameSettings.dialogueBlur.toFixed(1)})</label><input type="range" id="dialogueBlur" name="dialogueBlur" min="0" max="20" step="0.5" value={gameSettings.dialogueBlur} onChange={handleGameSettingValueChange} className="w-full h-2 bg-element-themed rounded-lg appearance-none cursor-pointer accent-accent-themed" /></div>
          <div className="flex items-center justify-between"><label htmlFor="enableBackdropBlur" className={labelClass}>{UIText.enableBackdropBlurLabel}</label><input type="checkbox" id="enableBackdropBlur" name="enableBackdropBlur" checked={gameSettings.enableBackdropBlur} onChange={handleGameSettingValueChange} className="h-4 w-4 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed" /></div>
          <div className="flex items-center justify-between"><label htmlFor="enableImageGeneration" className={labelClass}>{UIText.enableBackgroundImageGenerationLabel}</label><input type="checkbox" id="enableImageGeneration" name="enableImageGeneration" checked={gameSettings.enableImageGeneration} onChange={handleGameSettingValueChange} className="h-4 w-4 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed" /></div>
          <div><label htmlFor="selectedImagePromptStyleId" className={labelClass}>{UIText.imagePromptStyleSelectionLabel}</label><select id="selectedImagePromptStyleId" name="selectedImagePromptStyleId" value={gameSettings.selectedImagePromptStyleId} onChange={handleGameSettingValueChange} className={inputClass} disabled={!gameSettings.enableImageGeneration}>{AVAILABLE_IMAGE_PROMPT_STYLES.map(style => <option key={style.id} value={style.id}>{style.name}</option>)}</select></div>
          <div><label htmlFor="imageGenerationInterval" className={labelClass}>{UIText.imageGenerationIntervalLabel} ({gameSettings.imageGenerationInterval})</label><input type="range" id="imageGenerationInterval" name="imageGenerationInterval" min="0" max="20" step="1" value={gameSettings.imageGenerationInterval} onChange={handleGameSettingValueChange} className="w-full h-2 bg-element-themed rounded-lg appearance-none cursor-pointer accent-accent-themed" disabled={!gameSettings.enableImageGeneration} /></div>
          <div><label htmlFor="minOutputChars" className={labelClass}>{UIText.minOutputCharsLabel} ({gameSettings.minOutputChars})</label><input type="range" id="minOutputChars" name="minOutputChars" min="30" max="1500" step="10" value={gameSettings.minOutputChars} onChange={handleGameSettingValueChange} className="w-full h-2 bg-element-themed rounded-lg appearance-none cursor-pointer accent-accent-themed" /></div>
          <div><label htmlFor="maxOutputChars" className={labelClass}>{UIText.maxOutputCharsLabel} ({gameSettings.maxOutputChars})</label><input type="range" id="maxOutputChars" name="maxOutputChars" min="500" max="5000" step="50" value={gameSettings.maxOutputChars} onChange={handleGameSettingValueChange} className="w-full h-2 bg-element-themed rounded-lg appearance-none cursor-pointer accent-accent-themed" /></div>
          <div className="flex items-center justify-between"><label htmlFor="enableStreamMode" className={labelClass}>{UIText.enableStreamModeLabel}</label><input type="checkbox" id="enableStreamMode" name="enableStreamMode" checked={gameSettings.enableStreamMode === undefined ? DefaultGameSettings.enableStreamMode : gameSettings.enableStreamMode} onChange={handleGameSettingValueChange} className="h-4 w-4 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed" /></div>
          {gameSettings.enableStreamMode && (<div className="flex items-center justify-between pl-4"><label htmlFor="enablePseudoStreamMode" className={`${labelClass} text-xs`}>{UIText.enablePseudoStreamModeLabel}</label><input type="checkbox" id="enablePseudoStreamMode" name="enablePseudoStreamMode" checked={gameSettings.enablePseudoStreamMode === undefined ? DefaultGameSettings.enablePseudoStreamMode : gameSettings.enablePseudoStreamMode} onChange={handleGameSettingValueChange} className="h-3.5 w-3.5 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed" /></div>)}
        </>
      ), undefined, Icons.AdjustmentsHorizontal)}

      {renderSectionWithToggle('characterCardRoot', UIText.characterCardSectionTitle,
        (<>
          <PresetSection type="characterCard" presets={characterCardPresets} onLoadPreset={onLoadPreset} onDeletePreset={handleDeletePresetClick} activePresetId={activeCharCardPresetId}/>
          {renderCollapsibleDetails('currentCharacterCardDetails', '编辑角色卡详情', activeCharCardPreset,
            () => handleOpenPresetNameModal('characterCard', getCurrentCharacterCardData()),
            (<>
              {characterCardFields.map(field => (
                <div key={field.key}>
                  <label htmlFor={field.key} className={labelClass}>{field.label}</label>
                  {field.type === 'textarea' ? (
                    <textarea id={field.key} name={field.key} value={gameSettings[field.key as keyof GameSettingsData] as string || ''} onChange={handleCharacterCardValueChange} placeholder={field.placeholder} rows={field.rows || 3} className={textareaClass} />
                  ) : (
                    <input type="text" id={field.key} name={field.key} value={gameSettings[field.key as keyof GameSettingsData] as string || ''} onChange={handleCharacterCardValueChange} placeholder={field.placeholder} className={inputClass} />
                  )}
                </div>
              ))}
            </>),
            activeCharCardPreset ? () => handleOpenPresetNameModal('characterCard', getCurrentCharacterCardData(), activeCharCardPreset.id) : undefined
          )}
        </>), undefined, Icons.CharacterCardIcon, searchButtonCharCardRef, () => openSearchDropdown('characterCard'), undefined, activeCharCardPreset?.name
      )}

      {renderSectionWithToggle('userRoleRoot', UIText.userRoleLabel,
        (<>
          <PresetSection type="userRole" presets={userRolePresets} onLoadPreset={onLoadPreset} onDeletePreset={handleDeletePresetClick} activePresetId={activeUserRolePresetId}/>
          {renderCollapsibleDetails('currentUserRoleDetails', '编辑“我”的设定', activeUserRolePreset,
            () => handleOpenPresetNameModal('userRole', gameSettings.userRole),
            (<textarea id="userRole" name="userRole" value={gameSettings.userRole} onChange={handleGameSettingValueChange} placeholder={UIText.userRolePlaceholder} rows={5} className={textareaClass} />),
            activeUserRolePreset ? () => handleOpenPresetNameModal('userRole', gameSettings.userRole, activeUserRolePreset.id) : undefined
          )}
        </>), undefined, Icons.Profile, searchButtonUserRoleRef, () => openSearchDropdown('userRole'), undefined, activeUserRolePreset?.name
      )}

      {renderSectionWithToggle('systemRoleRoot', UIText.systemRoleLabel,
        (<>
          <PresetSection type="systemRole" presets={aiStylePresets} onLoadPreset={onLoadPreset} onDeletePreset={handleDeletePresetClick} activePresetId={activeSystemRolePresetId}/>
          {renderCollapsibleDetails('currentSystemRoleDetails', '编辑“文风”设定', activeSystemRolePreset,
            () => handleOpenPresetNameModal('systemRole', gameSettings.systemRole),
            (<textarea id="systemRole" name="systemRole" value={gameSettings.systemRole} onChange={handleGameSettingValueChange} placeholder={UIText.systemRolePlaceholder} rows={8} className={textareaClass} />),
            activeSystemRolePreset ? () => handleOpenPresetNameModal('systemRole', gameSettings.systemRole, activeSystemRolePreset.id) : undefined
          )}
        </>), undefined, Icons.Sparkles, searchButtonSystemRoleRef, () => openSearchDropdown('systemRole'), undefined, activeSystemRolePreset?.name
      )}

      {renderSectionWithToggle('worldBookRoot', UIText.worldBookSectionTitle,
        (<>
          <div className="flex justify-start items-center space-x-1 mb-2">
            <button className={`${smallButtonClass}`} onClick={() => toggleAllWorldBookElements(true)}>{UIText.enableAllWorldBook}</button>
            <button className={`${smallButtonClass}`} onClick={() => toggleAllWorldBookElements(false)}>{UIText.disableAllWorldBook}</button>
          </div>
          <div className="flex items-center mb-2">
            <input type="text" value={newPrimaryElementName} onChange={(e) => setNewPrimaryElementName(e.target.value)} placeholder={UIText.primaryElementNamePlaceholder} className={`${inputClass} text-xs flex-grow mr-1.5`} />
            <button onClick={handleAddPrimaryElement} className={smallButtonClass} title={UIText.addNewElementSet}><Icons.PixelPlus className="w-3 h-3"/></button>
          </div>
          {(gameSettings.customNarrativeElements || []).length === 0 && (<p className="text-xs text-secondary-themed italic text-center">{UIText.noWorldBookSets}</p>)}
          <div className="space-y-1.5 max-h-96 overflow-y-auto pr-1">
            {(gameSettings.customNarrativeElements || []).map(primaryEl => (
              <div key={primaryEl.id} id={`worldbook-item-${primaryEl.id}`} className="p-1.5 bg-element-themed/20 rounded border border-themed/15">
                <div className="flex justify-between items-center mb-1">
                  <input type="checkbox" checked={primaryEl.isActive} onChange={() => handleTogglePrimaryElementActive(primaryEl.id)} className="h-3.5 w-3.5 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed mr-1.5 flex-shrink-0" />
                  {editingPrimaryId === primaryEl.id ? (
                    <input type="text" value={tempPrimaryName} onChange={e => setTempPrimaryName(e.target.value)} onBlur={handleSavePrimaryNameEdit} onKeyDown={e => e.key === 'Enter' && handleSavePrimaryNameEdit()} autoFocus className={`${inputClass} text-xs flex-grow`}/>
                  ) : (
                    <span onClick={() => handleEditPrimaryName(primaryEl)} className={`text-xs font-medium flex-grow cursor-text ${primaryEl.isActive ? 'text-primary-themed' : 'text-secondary-themed line-through'}`}>{primaryEl.name}</span>
                  )}
                  <div className="flex flex-shrink-0 ml-1 space-x-0.5">
                    <button onClick={() => toggleSectionCollapse(primaryEl.id)} className={`${iconButtonClass} p-0.5`}><Icons.ChevronDown className={`w-4 h-4 transform transition-transform ${collapsedSections[primaryEl.id] ? 'rotate-[-90deg]' : ''}`} /></button>
                    <button onClick={() => handleDeletePrimaryElement(primaryEl.id)} className={`${iconButtonClass} p-0.5`} title={UIText.deleteElementSet}><Icons.Trash className="w-3.5 h-3.5"/></button>
                  </div>
                </div>
                {!collapsedSections[primaryEl.id] && (
                  <div className="pl-3 space-y-1 border-l-2 border-themed/20 ml-1.5">
                    {(primaryEl.subElements || []).map(subEl => (
                      <div key={subEl.id} className="p-1 rounded bg-element-themed/10 border border-themed/10">
                        <div className="flex items-center mb-0.5">
                          <button onClick={() => toggleSectionCollapse(subEl.id)} className={`${iconButtonClass} p-0.5 mr-1`}>
                            <Icons.ChevronDown className={`w-3.5 h-3.5 transform transition-transform text-secondary-themed/70 ${collapsedSections[subEl.id] ? 'rotate-[-90deg]' : ''}`} />
                          </button>
                           <input type="checkbox" checked={subEl.isActive} onChange={() => handleToggleSubElementActive(primaryEl.id, subEl.id)} className="h-3 w-3 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed mr-1 flex-shrink-0" />
                           {editingSubKeyId === subEl.id ? (
                             <input ref={subKeyInputRef} type="text" value={tempSubElementKey} onChange={e => setTempSubElementKey(e.target.value)} onBlur={() => handleSaveSubKey(primaryEl.id)} onKeyDown={e => e.key === 'Enter' && handleSaveSubKey(primaryEl.id)} className={`${inputClass} text-xs py-0.5 px-1 flex-grow`}/>
                           ) : (
                             <span onClick={() => handleEditSubKey(subEl)} className={`text-xs font-medium cursor-text mr-1 ${subEl.isActive && primaryEl.isActive ? 'text-accent-themed/90' : 'text-secondary-themed/70 line-through'}`}>{subEl.key}:</span>
                           )}
                           <button onClick={() => handleDeleteSubElement(primaryEl.id, subEl.id)} className={`${iconButtonClass} p-0.5 ml-auto`} title={UIText.deleteElement}><Icons.Trash className="w-3 h-3"/></button>
                        </div>
                        {!collapsedSections[subEl.id] && (
                            editingSubValueId === subEl.id ? (
                            <textarea ref={subValueTextareaRef} value={tempSubElementValue} onChange={e => setTempSubElementValue(e.target.value)} onBlur={() => handleSaveSubValue(primaryEl.id)} className={`${textareaClass} text-xs py-0.5 px-1 ml-5`} rows={2}/>
                            ) : (
                            <p onClick={() => handleEditSubValue(subEl)} className={`text-xs whitespace-pre-wrap cursor-text ml-5 ${subEl.isActive && primaryEl.isActive ? 'text-secondary-themed' : 'text-secondary-themed/50 line-through'}`}>{subEl.value || '(空)'}</p>
                            )
                        )}
                      </div>
                    ))}
                    <div className="flex items-center pt-1">
                      <input type="text" value={subElementInputs[primaryEl.id]?.key || ''} onChange={e => handleSubElementInputChange(primaryEl.id, 'key', e.target.value)} placeholder={UIText.elementNamePlaceholder} className={`${inputClass} text-xs py-0.5 px-1 flex-grow mr-1 w-2/5`} />
                      <input type="text" value={subElementInputs[primaryEl.id]?.value || ''} onChange={e => handleSubElementInputChange(primaryEl.id, 'value', e.target.value)} placeholder={UIText.elementContentPlaceholder} className={`${inputClass} text-xs py-0.5 px-1 flex-grow mr-1 w-3/5`} />
                      <button onClick={() => handleAddSubElement(primaryEl.id)} className={`${smallButtonClass} p-0.5`} title={UIText.addSubElementToSet}><Icons.PixelPlus className="w-3 h-3"/></button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </>), undefined, Icons.BookOpen, searchButtonWorldBookRef, () => openSearchDropdown('worldBook'), handleReorderWorldBook
      )}



      {renderSectionWithToggle('regexRoot', UIText.regexReplacementsTitle,
        (<>
          <div className="flex items-center justify-between mb-2">
            <label htmlFor="enableRegexReplacement" className={labelClass}>{UIText.enableRegexReplacementsLabel}</label>
            <input
              type="checkbox"
              id="enableRegexReplacement"
              name="enableRegexReplacement"
              checked={gameSettings.enableRegexReplacement}
              onChange={handleGameSettingValueChange}
              className="h-4 w-4 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed"
            />
          </div>
          <button onClick={handleAddRegexRule} className={`${smallButtonClass} mb-2`}>
            <Icons.PixelPlus className="w-3 h-3 mr-1" />{UIText.addRegexRule}
          </button>
          {(gameSettings.regexRules || []).length === 0 && (
            <p className="text-xs text-secondary-themed italic text-center">{UIText.noRegexRules}</p>
          )}
          <div className="space-y-2 max-h-[500px] overflow-y-auto pr-1">
            {(gameSettings.regexRules || []).map(rule => (
              <div key={rule.id} className="p-1.5 bg-element-themed/20 rounded-md border border-themed/15">
                <div className="flex justify-between items-center">
                  <input
                    type="checkbox"
                    checked={rule.isActive}
                    onChange={(e) => handleRegexRuleChange(rule.id, 'isActive', e.target.checked)}
                    className="h-3.5 w-3.5 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed mr-2 flex-shrink-0"
                    title="启用/禁用此规则"
                  />
                  <input
                    type="text"
                    value={rule.name}
                    onChange={(e) => handleRegexRuleChange(rule.id, 'name', e.target.value)}
                    placeholder={UIText.regexRuleNamePlaceholder}
                    className={`${inputClass} text-xs font-medium flex-grow py-1`}
                    title={UIText.regexRuleNameLabel}
                  />
                  <button onClick={() => toggleSectionCollapse(`regex-${rule.id}`)} className={`${iconButtonClass} p-1 ml-1`}>
                    <Icons.ChevronDown className={`w-4 h-4 transform transition-transform text-secondary-themed ${collapsedSections[`regex-${rule.id}`] ? 'rotate-[-90deg]' : ''}`} />
                  </button>
                  <button onClick={() => handleDeleteRegexRule(rule.id)} className={`${iconButtonClass} p-1`} title={UIText.deleteRegexRule}>
                    <Icons.Trash className="w-3.5 h-3.5" />
                  </button>
                </div>

                {!collapsedSections[`regex-${rule.id}`] && (
                  <div className="mt-2 space-y-2 pl-3 border-l-2 border-themed/10 ml-1 animate-fadeIn">
                    <div>
                      <label className={`${labelClass} text-xs`}>{UIText.regexRulePatternLabel}</label>
                      <textarea
                        value={rule.pattern}
                        onChange={(e) => handleRegexRuleChange(rule.id, 'pattern', e.target.value)}
                        placeholder={UIText.regexRulePatternPlaceholder}
                        className={`${textareaClass} text-xs font-mono`}
                        rows={2}
                      />
                    </div>
                    <div>
                      <label className={`${labelClass} text-xs`}>{UIText.regexRuleReplacementLabel}</label>
                      <textarea
                        value={rule.replacement}
                        onChange={(e) => handleRegexRuleChange(rule.id, 'replacement', e.target.value)}
                        placeholder={UIText.regexRuleReplacementPlaceholder}
                        className={`${textareaClass} text-xs`}
                        rows={2}
                      />
                    </div>
                    <div>
                      <label className={`${labelClass} text-xs`}>{UIText.regexRuleTrimInputLabel}</label>
                      <textarea
                        value={rule.trimInput || ''}
                        onChange={(e) => handleRegexRuleChange(rule.id, 'trimInput', e.target.value)}
                        placeholder={UIText.regexRuleTrimInputPlaceholder}
                        className={`${textareaClass} text-xs`}
                        rows={2}
                        title={UIText.regexRuleTrimInputPlaceholder}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-x-3 gap-y-2 items-end">
                      <div>
                        <label className={`${labelClass} text-xs`}>{UIText.regexRuleFlagsLabel}</label>
                        <input
                          type="text"
                          value={rule.flags}
                          onChange={(e) => handleRegexRuleChange(rule.id, 'flags', e.target.value)}
                          placeholder={UIText.regexRuleFlagsPlaceholder}
                          className={`${inputClass} text-xs font-mono py-1`}
                        />
                      </div>
                      <div>
                        <label className={`${labelClass} text-xs`}>{UIText.regexRuleScopeLabel}</label>
                        <select
                          value={rule.scope}
                          onChange={(e) => handleRegexRuleChange(rule.id, 'scope', e.target.value as RegexRuleScope)}
                          className={`${inputClass} text-xs appearance-none py-1`}
                        >
                          <option value="input">{UIText.regexScopeInput}</option>
                          <option value="output">{UIText.regexScopeOutput}</option>
                          <option value="all">{UIText.regexScopeAll}</option>
                        </select>
                      </div>
                      <div className="col-span-2">
                        <label className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={rule.isDisplayOnly || false}
                            onChange={(e) => handleRegexRuleChange(rule.id, 'isDisplayOnly', e.target.checked)}
                            className="h-3.5 w-3.5 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed"
                          />
                          <span className={`${labelClass} !mb-0 text-xs`}>{UIText.regexRuleIsDisplayOnlyLabel}</span>
                        </label>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </>), undefined, Icons.WrenchScrewdriver
      )}

      <SearchDropdown
        isOpen={activeSearchDropdown === 'characterCard'}
        items={characterCardPresets}
        searchTerm={currentSearchTerm}
        onSearchTermChange={setCurrentSearchTerm}
        onSelectItem={handleSearchItemSelect}
        itemToString={(item) => item.name}
        itemToId={(item) => item.id}
        onClose={() => { setActiveSearchDropdown(null); setCurrentSearchTerm(''); }}
        anchorRef={getSearchButtonRef('characterCard')}
        placeholderText="搜索角色卡预设..."
      />
      <SearchDropdown
        isOpen={activeSearchDropdown === 'userRole'}
        items={userRolePresets}
        searchTerm={currentSearchTerm}
        onSearchTermChange={setCurrentSearchTerm}
        onSelectItem={handleSearchItemSelect}
        itemToString={(item) => item.name}
        itemToId={(item) => item.id}
        onClose={() => { setActiveSearchDropdown(null); setCurrentSearchTerm(''); }}
        anchorRef={getSearchButtonRef('userRole')}
        placeholderText="搜索“我”的设定预设..."
      />
      <SearchDropdown
        isOpen={activeSearchDropdown === 'systemRole'}
        items={aiStylePresets}
        searchTerm={currentSearchTerm}
        onSearchTermChange={setCurrentSearchTerm}
        onSelectItem={handleSearchItemSelect}
        itemToString={(item) => item.name}
        itemToId={(item) => item.id}
        onClose={() => { setActiveSearchDropdown(null); setCurrentSearchTerm(''); }}
        anchorRef={getSearchButtonRef('systemRole')}
        placeholderText="搜索“文风”设定预设..."
      />
      <SearchDropdown
        isOpen={activeSearchDropdown === 'worldBook'}
        items={(gameSettings.customNarrativeElements || [])}
        searchTerm={currentSearchTerm}
        onSearchTermChange={setCurrentSearchTerm}
        onSelectItem={(item) => handleSearchItemSelect(item as CustomNarrativePrimaryElement)}
        itemToString={(item) => (item as CustomNarrativePrimaryElement).name}
        itemToId={(item) => (item as CustomNarrativePrimaryElement).id}
        onClose={() => { setActiveSearchDropdown(null); setCurrentSearchTerm(''); }}
        anchorRef={getSearchButtonRef('worldBook')}
        placeholderText="搜索世界书元素集..."
      />


      {/* Preset Name Modal */}
      {presetNameModalState.isOpen && (
        <ConfirmationModal
          isOpen={true}
          title={UIText.presetNameModalTitle}
          onConfirm={handleConfirmPresetName}
          onCancel={handleClosePresetNameModal}
          confirmText={UIText.saveGame}
          cancelText={UIText.cancelAction}
          enableBackdropBlur={enableBackdropBlur}
        >
          <input
            ref={presetNameInputRef}
            type="text"
            value={presetNameModalState.tempName}
            onChange={(e) => setPresetNameModalState(prev => ({ ...prev, tempName: e.target.value }))}
            placeholder={UIText.enterPresetNamePrompt}
            className={inputClass}
            onKeyDown={e => e.key === 'Enter' && handleConfirmPresetName()}
          />
        </ConfirmationModal>
      )}
    </div>
  );
};
export default SettingsMenu;
