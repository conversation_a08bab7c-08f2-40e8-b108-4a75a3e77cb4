


import React, { useState, useEffect, useCallback, useContext, useRef } from 'react';
import { GameSettingsData, GamePhase, CustomNarrativePrimaryElement, DialogueLine, CharacterCardData, SillyTavernCharCardEntry, SillyTavernEntry } from './types';
import { UIText, DefaultGameSettings, START_SCREEN_BACKGROUND_KEYWORDS, InitialPlayerStatus, APP_TITLE_CN, Icons, GEMINI_MODEL_TEXT_FALLBACK } from './constants';

import ChatInterface from './components/ChatInterface';
import ImageDisplay from './components/ImageDisplay';
import SidebarMenu, { ActiveTab as SidebarActiveTabType } from './components/SidebarMenu';
import PersistentStatusDisplay from './components/PersistentStatusDisplay';
import StartScreen from '@/components/StartScreen';
import NotificationsContainer from './components/NotificationsContainer';
import ConfirmationModal from './components/ConfirmationModal';
import { AIProviderButton } from './components/AIProviderButton';
import InventoryModal from './components/InventoryModal';
import LocationsModal from './components/LocationsModal';
import QuestsModal from './components/QuestsModal';
import CharactersModal from './components/CharactersModal';
import ImportantEventsModal from './components/ImportantEventsModal';
import Dropdown from './components/Dropdown'; // Import new Dropdown component


import { ThemeContext } from './contexts/ThemeContext';
import { NotificationContext } from './contexts/NotificationContext';
import { useGameSettings } from './hooks/useGameSettings';
import { useUserPreferences } from './hooks/useUserPreferences';

import { useGameSaves } from './hooks/useGameSaves';
import { usePlayerStatus } from './hooks/usePlayerStatus';
import { useOpeningSequence } from './hooks/useOpeningSequence';
import { usePresets } from './hooks/usePresets';
import { useUIState } from './hooks/useUIState';
import { useGameSession } from './hooks/useGameSession';
import { useDataManagement } from './hooks/useDataManagement';

import { renderRichTextStatic } from './utils/textUtils';
import { convertDialogueLogToGeminiHistory } from './utils/geminiUtils';
import { summarizeStoryForContinuation } from './services/geminiService';
import { extractCharaDataFromPng } from './utils/pngCardParser'; // Import PNG parser


export const App: React.FC = () => {
  const { gameSettings, setGameSettings, persistGameSettings } = useGameSettings();
  const notificationContext = useContext(NotificationContext);
  if (!notificationContext) throw new Error("NotificationContext not found");
  const { addNotification } = notificationContext;

  const { userPreferences, setUserPreferences, persistUserPreferences, unlockAchievement, defaultUserPreferences } = useUserPreferences(addNotification);
  
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");
  const { theme, setTheme } = themeContext;

  const { 
    sidebarActiveTab, 
    setSidebarActiveTab,
    isSidebarOpen, 
    setIsSidebarOpen: setIsSidebarOpenFromHook, 
    toggleSidebar: toggleSidebarFromHook, 
    isStatusPanelOpen,
    statusPanelRef,
    setIsStatusPanelOpen,
    confirmationModalState,
    handleRequestConfirmation,
    closeConfirmationModal,
    activeDetailModal,
    setActiveDetailModal,
    closeDetailModal,
    isRenamingHeaderTitle,
    headerRenameValue,
    headerRenameInputRef,
    handleHeaderTitleClick,
    handleConfirmHeaderRename: handleConfirmHeaderRenameFromHook, 
    handleCancelHeaderRename: handleCancelHeaderRenameFromHook,   
    handleHeaderRenameKeyDown: handleHeaderRenameKeyDownFromHook, 
    setIsRenamingHeaderTitle,
    setHeaderRenameValue,
  } = useUIState(addNotification);

  const {
    gameSaves,
    currentQuickSaveId,
    saveGame,
    updateSave,
    loadGame: loadGameFromHook,
    deleteSave,
    renameSave,
    setCurrentQuickSaveId,
    setGameSaves: setGameSavesFromHook, 
  } = useGameSaves(gameSettings, userPreferences.unlockedAchievements, unlockAchievement, addNotification);
  
  const { 
    playerStatus, 
    setPlayerStatus,
    isSummarizing,
    allocateAttributePoint, 
    allocateSkillPoint,
    processStoryUpdateForRPG,
    handleSummarizeInventoryAndShowDetails,
    handleSummarizeLocationsAndShowDetails,
    handleSummarizeQuestsAndShowDetails,
    handleSummarizeCharactersAndShowDetails,
    handleSummarizeImportantEventsAndShowDetails,
    handleSummarizeAll,
  } = usePlayerStatus(addNotification, unlockAchievement, setActiveDetailModal);

  const { 
    dynamicOpeningLine, 
    fetchAndSetOpeningLine,
    openingLineHistory, 
    lastOpeningStyle,   
    setOpeningLineHistory, 
    setLastOpeningStyle    
  } = useOpeningSequence();
  
  const {
    characterCardPresets, userRolePresets, aiStylePresets, 
    savePreset, loadPreset, deletePreset,
    setCharacterCardPresets, setUserRolePresets, setAiStylePresets, 
  } = usePresets(gameSettings, setGameSettings, addNotification, unlockAchievement);

  const {
    gamePhase, setGamePhase,
    playerName, setPlayerName,
    dialogueLog, setDialogueLog,
    currentChoices, setCurrentChoices,
    chatSessionRef,
    isLoading, setIsLoading,
    currentSceneImageKeyword, setCurrentSceneImageKeyword,
    finalBackgroundUrl, setFinalBackgroundUrl,
    isLoadingBackground, setIsLoadingBackground,
    dialogueTurnCounterRef,
    initializeNewGame,
    handleSendMessage,
    handleDeleteDialogueLine,
    handleRegenerateResponse,
    // Editing state and handlers from useGameSession
    editingLineId,
    currentlyEditedContent,
    handleStartEditLine,
    handleSaveEditedLine,
    handleCancelEditLine,
    handleCurrentlyEditedContentChange,
  } = useGameSession(
    gameSettings, 
    setGameSettings,
    playerStatus,
    setPlayerStatus, 
    unlockAchievement, 
    addNotification, 
    processStoryUpdateForRPG,
    handleSummarizeAll,
    InitialPlayerStatus
  );
  
  const { 
    exportAllData, 
    importAllData, 
    handleResetAllSettingsToDefaults,
    handleInitializeGistBackup,
    handleBackupToGist,
    handleRestoreFromGist,
    isGistLoading,
  } = useDataManagement({
      gameSaves, gameSettings, userPreferences, theme, 
      characterCardPresets, 
      userRolePresets, aiStylePresets, 
      openingLineHistory, 
      lastOpeningStyle,   
      addNotification, unlockAchievement, handleRequestConfirmation,
      setGameSaves: setGameSavesFromHook, 
      setGameSettings, setUserPreferences, setTheme,
      setCharacterCardPresets,
      setUserRolePresets, setAiStylePresets, 
      setOpeningLineHistory, 
      setLastOpeningStyle,   
      defaultUserPreferences
  });

  const initialDataFetchedOnStartScreenRef = useRef(false);
  const autoGistBackupIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const [isRestartDropdownOpen, setIsRestartDropdownOpen] = useState(false);
  const restartDropdownRef = useRef<HTMLDivElement>(null);



  // Refs for Global Import file inputs
  const importCharCardFileRef = useRef<HTMLInputElement>(null);
  const importUserRoleFileRef = useRef<HTMLInputElement>(null);
  const importWorldBookFileRef = useRef<HTMLInputElement>(null);


  const prevGistAutoBackupEnabledRef = useRef<boolean | undefined>(gameSettings.enableGistAutoBackup);
  const prevGistAutoBackupIntervalRef = useRef<number | undefined>(gameSettings.gistAutoBackupIntervalHours);


  useEffect(() => {
    if (isSidebarOpen && sidebarActiveTab === 'saveload') {
    }
  }, [isSidebarOpen, sidebarActiveTab]);

  const persistAllSettings = useCallback(() => {
    persistGameSettings();
    persistUserPreferences();
  }, [persistGameSettings, persistUserPreferences]);

  useEffect(() => {
    const handleClickOutsideStatusPanel = (event: MouseEvent) => {
      if (isStatusPanelOpen && statusPanelRef.current && !statusPanelRef.current.contains(event.target as Node)) {
        const toggleButton = document.querySelector('header button[title="' + UIText.statusPanelToggle + '"]');
        if (toggleButton && toggleButton.contains(event.target as Node)) {
          return;
        }
        setIsStatusPanelOpen(false);
      }
    };

    if (isStatusPanelOpen) {
      document.addEventListener('mousedown', handleClickOutsideStatusPanel);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutsideStatusPanel);
    };
  }, [isStatusPanelOpen, setIsStatusPanelOpen, statusPanelRef]);

  const startNewGame = useCallback(() => {
    if (!playerName.trim()) { addNotification(UIText.storyStartError, 'error'); return; }
    initializeNewGame(playerName, gameSettings, {...InitialPlayerStatus, name: playerName });
  }, [playerName, gameSettings, initializeNewGame, addNotification]);

  const loadGame = useCallback((saveId: string) => {
    const saveData = loadGameFromHook(saveId);
    if (saveData) {
      const gameSpecificSettings: GameSettingsData = {
        characterName: saveData.characterName,
        characterDescription: saveData.characterDescription,
        characterOpeningMessage: saveData.characterOpeningMessage,
        characterPersonality: saveData.characterPersonality,
        characterScenario: saveData.characterScenario,
        characterExampleDialogue: saveData.characterExampleDialogue,
        characterPortraitKeywords: saveData.characterPortraitKeywords,
        userRole: saveData.userRole,
        systemRole: saveData.systemRole,
        customNarrativeElements: saveData.customNarrativeElements,
        selectedModelId: saveData.selectedModelId,
        selectedImagePromptStyleId: saveData.selectedImagePromptStyleId,
        selectedSummaryModelId: saveData.selectedSummaryModelId,
        chatInterfaceOpacity: saveData.chatInterfaceOpacity,
        dialogueBubbleOpacity: saveData.dialogueBubbleOpacity,
        dialogueBlur: saveData.dialogueBlur,
        fontSizeScale: saveData.fontSizeScale,
        enableBackdropBlur: saveData.enableBackdropBlur,
        enableImageGeneration: saveData.enableImageGeneration,
        minOutputChars: saveData.minOutputChars,
        maxOutputChars: saveData.maxOutputChars ?? DefaultGameSettings.maxOutputChars,
        imageGenerationInterval: saveData.imageGenerationInterval,
        enableStreamMode: saveData.enableStreamMode,
        enablePseudoStreamMode: saveData.enablePseudoStreamMode,
        githubPat: saveData.githubPat,
        gistId: saveData.gistId,
        saveGithubPat: saveData.saveGithubPat,
        enableGistAutoBackup: saveData.enableGistAutoBackup,
        gistAutoBackupIntervalHours: saveData.gistAutoBackupIntervalHours,
        gistUseSystemProxy: saveData.gistUseSystemProxy ?? DefaultGameSettings.gistUseSystemProxy,
        enableRegexReplacement: saveData.enableRegexReplacement ?? DefaultGameSettings.enableRegexReplacement,
        regexRules: saveData.regexRules ?? DefaultGameSettings.regexRules,
      };
      initializeNewGame(
        saveData.playerName,
        gameSpecificSettings,
        saveData.currentPlayerStatus,
        saveData.dialogueLog,
        saveData.currentSceneImageKeyword
      );
    }
  }, [loadGameFromHook, initializeNewGame]);


  const restartFromSaveSettings = useCallback((saveId: string) => {
    const settingsSourceSave = gameSaves.find(s => s.id === saveId);
    if (settingsSourceSave) {
        handleRequestConfirmation(UIText.restartGameTooltip, `${UIText.restartGameConfirmMessage} 这将使用存档《${settingsSourceSave.name}》中的叙事设定。`, () => {
            const { ...relevantSaveData } = settingsSourceSave;
            const settingsFromSave: GameSettingsData = {
              characterName: relevantSaveData.characterName,
              characterDescription: relevantSaveData.characterDescription,
              characterOpeningMessage: relevantSaveData.characterOpeningMessage,
              characterPersonality: relevantSaveData.characterPersonality,
              characterScenario: relevantSaveData.characterScenario,
              characterExampleDialogue: relevantSaveData.characterExampleDialogue,
              characterPortraitKeywords: relevantSaveData.characterPortraitKeywords,
              userRole: relevantSaveData.userRole, systemRole: relevantSaveData.systemRole,
              customNarrativeElements: relevantSaveData.customNarrativeElements,
              selectedModelId: relevantSaveData.selectedModelId, selectedImagePromptStyleId: relevantSaveData.selectedImagePromptStyleId, selectedSummaryModelId: relevantSaveData.selectedSummaryModelId,
              chatInterfaceOpacity: relevantSaveData.chatInterfaceOpacity, dialogueBubbleOpacity: relevantSaveData.dialogueBubbleOpacity, dialogueBlur: relevantSaveData.dialogueBlur,
              fontSizeScale: relevantSaveData.fontSizeScale, enableBackdropBlur: relevantSaveData.enableBackdropBlur, enableImageGeneration: relevantSaveData.enableImageGeneration,
              minOutputChars: relevantSaveData.minOutputChars,
              maxOutputChars: relevantSaveData.maxOutputChars ?? DefaultGameSettings.maxOutputChars,
              imageGenerationInterval: relevantSaveData.imageGenerationInterval,
              enableStreamMode: relevantSaveData.enableStreamMode,
              enablePseudoStreamMode: relevantSaveData.enablePseudoStreamMode,
              githubPat: relevantSaveData.githubPat,
              gistId: relevantSaveData.gistId,
              saveGithubPat: relevantSaveData.saveGithubPat,
              enableGistAutoBackup: relevantSaveData.enableGistAutoBackup,
              gistAutoBackupIntervalHours: relevantSaveData.gistAutoBackupIntervalHours,
              gistUseSystemProxy: relevantSaveData.gistUseSystemProxy ?? DefaultGameSettings.gistUseSystemProxy,
              enableRegexReplacement: relevantSaveData.enableRegexReplacement ?? DefaultGameSettings.enableRegexReplacement,
              regexRules: relevantSaveData.regexRules ?? DefaultGameSettings.regexRules,
            };
            initializeNewGame(playerName, settingsFromSave, {...InitialPlayerStatus, name: playerName}); 
            addNotification(`已使用《${relevantSaveData.name}》的设定重新开始游戏。`, "info");
        });
    } else { addNotification("无法找到源存档的设定。", "error"); }
  }, [gameSaves, playerName, initializeNewGame, addNotification, handleRequestConfirmation]);
  
  const handleQuickSaveGame = useCallback(async () => {
    const currentGeminiHistory = convertDialogueLogToGeminiHistory(dialogueLog);
    if (currentQuickSaveId) {
        if (gameSaves.find(s => s.id === currentQuickSaveId)) updateSave(currentQuickSaveId, playerName, dialogueLog, currentSceneImageKeyword, playerStatus, currentGeminiHistory);
        else { const newId = await saveGame(`${UIText.saveGame.split(' ')[0]} - ${new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`, playerName, dialogueLog, currentSceneImageKeyword, playerStatus, currentGeminiHistory); if (newId) setCurrentQuickSaveId(newId); }
    } else { const newId = await saveGame(`${UIText.saveGame.split(' ')[0]} - ${new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`, playerName, dialogueLog, currentSceneImageKeyword, playerStatus, currentGeminiHistory); if (newId) setCurrentQuickSaveId(newId); }
  }, [currentQuickSaveId, saveGame, updateSave, gameSaves, playerName, dialogueLog, currentSceneImageKeyword, playerStatus, setCurrentQuickSaveId]);

  const handleRestartGameRequest = useCallback(() => {
    handleRequestConfirmation(UIText.restartGameConfirmTitle, UIText.restartGameConfirmMessage, () => {
        initializeNewGame(playerName, gameSettings, {...InitialPlayerStatus, name: playerName});
        addNotification("游戏已使用当前设定重新开始。", "info");
    });
  }, [playerName, gameSettings, initializeNewGame, addNotification, handleRequestConfirmation]);

  const handleDefaultRestartGameRequest = useCallback(() => {
    handleRequestConfirmation(
      UIText.restartWithDefaultSettingsConfirmTitle,
      UIText.restartWithDefaultSettingsConfirmMessage, 
      () => {
        initializeNewGame(playerName, { ...DefaultGameSettings }, { ...InitialPlayerStatus, name: playerName });
        addNotification("游戏已使用默认设定重新开始。", "info");
      }
    );
  }, [playerName, initializeNewGame, addNotification, handleRequestConfirmation]);
  
  const handleOpenLoadMenu = useCallback(() => { setSidebarActiveTab('saveload'); setIsSidebarOpenFromHook(true); }, [setSidebarActiveTab, setIsSidebarOpenFromHook]);

  const handleRestartWithSummary = useCallback(async () => {
    handleRequestConfirmation(
        UIText.restartWithSummaryConfirmTitle,
        UIText.restartWithSummaryConfirmMessage, 
        async () => {
            setIsLoading(true); 
            try {
                const summary = await summarizeStoryForContinuation(
                    dialogueLog,
                    playerStatus,
                    gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK
                );

                const statusForRestart = { ...playerStatus, name: playerName };
                const initialDialog: DialogueLine[] = [{
                    id: 'summary-restart-0', speakerName: UIText.narrator, speakerType: 'narrator',
                    text: summary, 
                    processedHtml: renderRichTextStatic(summary),
                    timestamp: Date.now(),
                    storyUpdate: "游戏从AI生成的剧情摘要继续。"
                }];
                initializeNewGame(playerName, gameSettings, statusForRestart, initialDialog, START_SCREEN_BACKGROUND_KEYWORDS[0] );
                addNotification("游戏已从AI生成的剧情摘要重新开始。", "success");
            } catch (error: any) {
                console.error("Error restarting with summary:", error);
                addNotification(UIText.errorSummarizationFailed + (error.message ? `: ${error.message}` : ''), "error", 7000);
            } finally {
                setIsLoading(false); 
            }
        }
    );
  }, [playerName, gameSettings, playerStatus, dialogueLog, initializeNewGame, addNotification, handleRequestConfirmation, setIsLoading]);

  const currentSaveForHeader = gameSaves.find(s => s.id === currentQuickSaveId);
  const displayHeaderTitle = (gamePhase === GamePhase.Playing && currentSaveForHeader) ? currentSaveForHeader.name : APP_TITLE_CN;
  const canSpendPoints = playerStatus.attributePoints > 0 || playerStatus.skillPoints > 0;

  useEffect(() => {
    if (gamePhase === GamePhase.StartScreen && !initialDataFetchedOnStartScreenRef.current) {
      fetchAndSetOpeningLine();
      initialDataFetchedOnStartScreenRef.current = true;
    }
  }, [gamePhase, fetchAndSetOpeningLine]);
  
  const handleSidebarClose = useCallback(() => {
    persistAllSettings();
    setIsSidebarOpenFromHook(false);
  }, [setIsSidebarOpenFromHook, persistAllSettings]);

  // Destructure specific Gist settings for the useEffect dependency array
  const {
    enableGistAutoBackup,
    gistAutoBackupIntervalHours,
    githubPat,
    gistId
  } = gameSettings;

  useEffect(() => {
    if (autoGistBackupIntervalRef.current) {
      clearInterval(autoGistBackupIntervalRef.current);
      autoGistBackupIntervalRef.current = null;
    }

    const canSetupAutoBackup = gamePhase === GamePhase.Playing &&
                               enableGistAutoBackup &&
                               gistAutoBackupIntervalHours && gistAutoBackupIntervalHours > 0 &&
                               githubPat &&
                               gistId;

    if (canSetupAutoBackup) {
      const justEnabled = prevGistAutoBackupEnabledRef.current === false && enableGistAutoBackup === true;
      const intervalChangedWhileEnabled = enableGistAutoBackup === true && prevGistAutoBackupIntervalRef.current !== undefined && prevGistAutoBackupIntervalRef.current !== gistAutoBackupIntervalHours;
      
      if (justEnabled || intervalChangedWhileEnabled) {
        addNotification(UIText.gistAutoBackupInfo(gistAutoBackupIntervalHours), 'success', 5000);
      }

      const intervalMilliseconds = gistAutoBackupIntervalHours * 60 * 60 * 1000;
      const performAutoBackup = async () => {
        if (isGistLoading) return;
        addNotification(UIText.gistAutoBackupInfo(gistAutoBackupIntervalHours), 'info', 4000);
        await handleBackupToGist(true); 
      };
      autoGistBackupIntervalRef.current = setInterval(performAutoBackup, intervalMilliseconds);
    } else if (enableGistAutoBackup && (!githubPat || !gistId || !gistAutoBackupIntervalHours || gistAutoBackupIntervalHours <= 0)) {
      addNotification(UIText.gistAutoBackupConfigNeeded, 'warning', 7000);
    }

    prevGistAutoBackupEnabledRef.current = enableGistAutoBackup;
    prevGistAutoBackupIntervalRef.current = gistAutoBackupIntervalHours;

    return () => {
      if (autoGistBackupIntervalRef.current) {
        clearInterval(autoGistBackupIntervalRef.current);
      }
    };
  }, [
    gamePhase,
    enableGistAutoBackup, 
    gistAutoBackupIntervalHours, 
    githubPat, 
    gistId, 
    handleBackupToGist, 
    addNotification,    
    isGistLoading,      
  ]);

  useEffect(() => {
    const handleClickOutsideRestartDropdown = (event: MouseEvent) => {
      if (isRestartDropdownOpen && restartDropdownRef.current && !restartDropdownRef.current.contains(event.target as Node)) {
        const triggerButton = document.getElementById('restart-game-button-trigger');
        if (triggerButton && triggerButton.contains(event.target as Node)) {
          return;
        }
        setIsRestartDropdownOpen(false);
      }
    };
    if (isRestartDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutsideRestartDropdown);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutsideRestartDropdown);
    };
  }, [isRestartDropdownOpen]);

  const toggleRestartDropdown = () => setIsRestartDropdownOpen(prev => !prev);

  const selectRestartOption = (action: () => void) => {
    action();
    setIsRestartDropdownOpen(false);
  };

  useEffect(() => {
    const rootEl = document.documentElement;
    if (!gameSettings.enableImageGeneration) {
      rootEl.classList.add('no-image-bg');
    } else {
      rootEl.classList.remove('no-image-bg');
    }
  }, [gameSettings.enableImageGeneration]);

  // --- Global Import Logic (Moved from SettingsMenu) ---
  const getFilenameWithoutExtension = (filename: string): string => {
    return filename.replace(/\.(jsonc?|png)$/i, '');
  };

  const importWorldBookEntries = useCallback((
    entries: SillyTavernCharCardEntry[] | Record<string, SillyTavernEntry>,
    sourceName: string,
    importSourceType: 'character_book' | 'lorebook' | 'direct_entries_array' | 'direct_entries_object_map'
  ) => {
      const worldBookSetName = sourceName;
      const newPrimaryElement: CustomNarrativePrimaryElement = {
        id: `primary_wbimport_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
        name: worldBookSetName,
        isActive: true,
        subElements: [],
      };
      let subElementIndex = 0;
  
      const processEntry = (entryData: SillyTavernCharCardEntry | SillyTavernEntry, objectKeyName?: string) => {
          const keySource = (entryData as SillyTavernCharCardEntry).keys || (entryData as SillyTavernEntry).key;
          const subElementKey = entryData.comment || (keySource && keySource.length > 0 ? keySource[0] : (objectKeyName || `规则 ${subElementIndex + 1}`));
          newPrimaryElement.subElements.push({
            id: `sub_wbimport_${Date.now()}_${subElementIndex++}_${Math.random().toString(36).substring(2,5)}`,
            key: subElementKey.trim(),
            value: (entryData.content || '').trim(),
            isActive: typeof entryData.disable === 'boolean' ? !entryData.disable : true,
          });
      };
  
      if (Array.isArray(entries)) (entries as Array<SillyTavernCharCardEntry | SillyTavernEntry>).forEach(entry => processEntry(entry));
      else for (const entryId in entries) if (Object.prototype.hasOwnProperty.call(entries, entryId)) processEntry(entries[entryId], entryId);
  
      if (newPrimaryElement.subElements.length > 0) {
          setGameSettings(prevSettings => ({
            ...prevSettings,
            customNarrativeElements: [...(prevSettings.customNarrativeElements || []), newPrimaryElement],
          }));
          const sourceDetailMap = {'character_book': "character_book 区块", 'lorebook': "lorebook 区块", 'direct_entries_array': "直接 entries 数组", 'direct_entries_object_map': "直接 entries 对象"};
          addNotification(`${UIText.sillyTavernCharCardWorldBookImportSuccess(sourceName.split(' ')[0].replace('[','').replace(']',''), newPrimaryElement.name)} (源自: ${sourceDetailMap[importSourceType] || importSourceType})`, "success", 6000);
      } else {
          addNotification(UIText.sillyTavernCharCardWorldBookImportNotFound(sourceName), "info");
      }
  }, [setGameSettings, addNotification]);

  const processImportedCharacterCardData = useCallback((cardData: SillyTavernCharCard, fileName: string) => {
    const newCharCardSettings: Partial<CharacterCardData> = {
        characterName: cardData.name || cardData.char_name || gameSettings.characterName,
        characterDescription: (cardData.description || (cardData.char_persona && cardData.char_persona.length > (cardData.description || "").length ? cardData.char_persona : cardData.description)) || gameSettings.characterDescription,
        characterPersonality: (cardData.personality || (cardData.char_persona && cardData.char_persona !== (cardData.description || "") && cardData.personality ? cardData.personality : "")) || gameSettings.characterPersonality,
        characterOpeningMessage: cardData.first_mes || cardData.char_greeting || gameSettings.characterOpeningMessage,
        characterScenario: cardData.scenario || cardData.world_scenario || gameSettings.characterScenario,
        characterExampleDialogue: cardData.mes_example || cardData.example_dialogue || gameSettings.characterExampleDialogue,
        characterPortraitKeywords: ((cardData.name ? `${cardData.name}, ` : "") + (cardData.tags?.join(', ') || "")).trim().length > 0 ? ((cardData.name ? `${cardData.name}, ` : "") + (cardData.tags?.join(', ') || "")) : gameSettings.characterPortraitKeywords
    };
    const fullImportedCardData: CharacterCardData = { characterName: newCharCardSettings.characterName!, characterDescription: newCharCardSettings.characterDescription!, characterOpeningMessage: newCharCardSettings.characterOpeningMessage!, characterPersonality: newCharCardSettings.characterPersonality!, characterScenario: newCharCardSettings.characterScenario!, characterExampleDialogue: newCharCardSettings.characterExampleDialogue!, characterPortraitKeywords: newCharCardSettings.characterPortraitKeywords! };
    setGameSettings(prev => ({ ...prev, ...fullImportedCardData }));
    savePreset('characterCard', fullImportedCardData, getFilenameWithoutExtension(fileName));
    addNotification(UIText.sillyTavernCharCardImportSuccess(fileName), "success");

    const charNameForWBSource = newCharCardSettings.characterName || fileName;
    let worldBookImported = false;
    if (cardData.character_book?.entries?.length) { importWorldBookEntries(cardData.character_book.entries, `[${charNameForWBSource}] ${UIText.worldBookSectionTitle}`, 'character_book'); worldBookImported = true; }
    else if (cardData.lorebook?.entries?.length) { importWorldBookEntries(cardData.lorebook.entries, `[${charNameForWBSource}] ${UIText.worldBookSectionTitle}`, 'lorebook'); worldBookImported = true; }
    else if (cardData.entries) {
        if (Array.isArray(cardData.entries) && cardData.entries.length > 0) { importWorldBookEntries(cardData.entries as SillyTavernCharCardEntry[], `[${charNameForWBSource}] ${UIText.worldBookSectionTitle}`, 'direct_entries_array'); worldBookImported = true; }
        else if (typeof cardData.entries === 'object' && !Array.isArray(cardData.entries) && Object.keys(cardData.entries).length > 0) { importWorldBookEntries(cardData.entries as Record<string, SillyTavernEntry>, `[${charNameForWBSource}] ${UIText.worldBookSectionTitle}`, 'direct_entries_object_map'); worldBookImported = true; }
    }
  }, [gameSettings, setGameSettings, addNotification, importWorldBookEntries, savePreset]);

  const handleImportSillyTavernCharCard = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files; if (!files || files.length === 0) return;
    Array.from(files).forEach(file => {
        const reader = new FileReader(); const fileName = file.name;
        const processFileContent = (content: string | ArrayBuffer | null, isPng: boolean) => {
            try {
                let cardData: SillyTavernCharCard;
                if (isPng && content instanceof ArrayBuffer) {
                    const base64Data = extractCharaDataFromPng(content);
                    if (!base64Data) { addNotification(`${UIText.sillyTavernCharCardImportError} (${fileName}: PNG数据无效)`, "error"); return; }
                    const decodedJsonString = new TextDecoder('utf-8').decode(Uint8Array.from(atob(base64Data), c => c.charCodeAt(0)));
                    cardData = JSON.parse(decodedJsonString);
                } else if (!isPng && typeof content === 'string') {
                    cardData = JSON.parse(content);
                } else { throw new Error("无法读取文件内容"); }
                processImportedCharacterCardData(cardData, fileName);
            } catch (error: any) { addNotification(`${UIText.sillyTavernCharCardImportError} (${fileName}: ${error.message})`, "error"); }
        };
        if (fileName.toLowerCase().endsWith('.png')) { reader.onload = (e) => processFileContent(e.target?.result as ArrayBuffer, true); reader.readAsArrayBuffer(file); }
        else if (fileName.toLowerCase().endsWith('.json') || fileName.toLowerCase().endsWith('.jsonc')) { reader.onload = (e) => processFileContent(e.target?.result as string, false); reader.readAsText(file, 'UTF-8'); }
        else { addNotification(`${UIText.sillyTavernCharCardImportError} (${fileName}: 不支持的文件格式)`, "error"); }
    });
    if (event.target) event.target.value = "";
  }, [processImportedCharacterCardData, addNotification]);

  const handleImportUserRoleCharCard = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]; if (!file) return;
    const reader = new FileReader();
    const processCardDataForUserRole = (cardData: SillyTavernCharCard, fileName: string) => {
        let userRoleText = (cardData.description || cardData.char_persona || "").replace(/\{\{char\}\}/gi, "{{user}}").replace(/<CHAR>/gi, "{{user}}");
        if (cardData.personality?.trim()) userRoleText += (userRoleText ? "\n" : "") + `性格：${cardData.personality}`;
        if (cardData.first_mes?.trim()) userRoleText += (userRoleText ? "\n" : "") + `初次见面时可能会说：${cardData.first_mes}`;
        setGameSettings(prev => ({ ...prev, userRole: userRoleText.trim() }));
        savePreset('userRole', userRoleText.trim(), getFilenameWithoutExtension(fileName));
        addNotification(`已从角色卡 [${fileName}] 的设定更新玩家角色描述。`, "success");
    };
    if (file.name.toLowerCase().endsWith('.png')) {
        reader.onload = (e) => { try { const data = extractCharaDataFromPng(e.target?.result as ArrayBuffer); if (data) processCardDataForUserRole(JSON.parse(new TextDecoder('utf-8').decode(Uint8Array.from(atob(data), c => c.charCodeAt(0)))), file.name); else throw new Error("PNG数据无效"); } catch (err:any) { addNotification(UIText.sillyTavernCharCardImportError + ` (${err.message})`, "error"); } finally { if (event.target) event.target.value = ""; } };
        reader.readAsArrayBuffer(file);
    } else if (file.name.toLowerCase().endsWith('.json') || file.name.toLowerCase().endsWith('.jsonc')) {
        reader.onload = (e) => { try { processCardDataForUserRole(JSON.parse(e.target?.result as string), file.name); } catch (err:any) { addNotification(UIText.sillyTavernCharCardImportError + ` (${err.message})`, "error"); } finally { if (event.target) event.target.value = ""; } };
        reader.readAsText(file, 'UTF-8');
    } else { addNotification(UIText.sillyTavernCharCardImportError + " (不支持的文件格式)", "error"); if (event.target) event.target.value = ""; }
  }, [setGameSettings, addNotification, savePreset]);

  const handleImportSillyTavernWorldBook = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files; if (!files || files.length === 0) return;
    Array.from(files).forEach(file => {
        const reader = new FileReader(); const fileName = file.name;
        if (fileName.toLowerCase().endsWith('.json')) {
            reader.onload = (e) => {
                try { const worldBookData = JSON.parse(e.target?.result as string); if (worldBookData?.entries && typeof worldBookData.entries === 'object') importWorldBookEntries(worldBookData.entries, getFilenameWithoutExtension(fileName), 'direct_entries_object_map'); else addNotification(`${UIText.sillyTavernImportError} (${fileName}: 格式无效)`, "error"); }
                catch (error: any) { addNotification(`${UIText.sillyTavernImportError} (${fileName}: ${error.message})`, "error"); }
            }; reader.readAsText(file, 'UTF-8');
        } else { addNotification(`${UIText.sillyTavernImportError} (${fileName}: 请选择.json文件)`, "error"); }
    });
    if (event.target) event.target.value = "";
  }, [addNotification, importWorldBookEntries]);

  const globalImportDropdownItems = [
    { id: 'importCharCard', label: UIText.importSillyTavernCharCard, icon: () => <span role="img" aria-label="Character Card">🃏</span>, onClick: () => importCharCardFileRef.current?.click() },
    { id: 'importUserRoleFromCard', label: UIText.importUserRoleFromCharCardButtonLabel, icon: () => <span role="img" aria-label="User Settings">🧑‍🔧</span>, onClick: () => importUserRoleFileRef.current?.click() },
    { id: 'importWorldBook', label: UIText.importSillyTavernWorldBook, icon: () => <span role="img" aria-label="World Book">📚</span>, onClick: () => importWorldBookFileRef.current?.click() },
  ];
  // --- End Global Import Logic ---


  if (gamePhase === GamePhase.StartScreen) {
    return (
      <StartScreen
        playerName={playerName}
        onPlayerNameChange={setPlayerName}
        dynamicOpeningLine={dynamicOpeningLine}
        finalBackgroundUrl={finalBackgroundUrl}
        isLoadingBackground={isLoadingBackground}
        onStartGame={startNewGame}
        gameSaves={gameSaves}
        onSaveGame={(name) => saveGame(name, playerName, dialogueLog, currentSceneImageKeyword, playerStatus, convertDialogueLogToGeminiHistory(dialogueLog))}
        onLoadGame={loadGame}
        onDeleteSave={deleteSave}
        onRenameSave={renameSave}
        addNotification={addNotification}
        onRequestConfirmation={handleRequestConfirmation}
        gameSettings={gameSettings} 
        userPreferences={userPreferences}
        onGameSettingsChange={setGameSettings}
        onUserPreferencesChange={setUserPreferences} 
        onExportData={exportAllData}
        onImportData={importAllData}
        onResetAllSettingsToDefaults={() => handleResetAllSettingsToDefaults()} 
      />
    );
  }

  return (
    <div className="app-container">
      <div className="absolute inset-0 -z-10">
        <ImageDisplay finalImageUrl={finalBackgroundUrl} />
      </div>
      
      <header className="app-header">
        <button onClick={toggleSidebarFromHook} className="menu-button" title={UIText.menu}>
          <Icons.Menu className="w-7 h-7" />
        </button>
        <div className="app-title-header flex-grow text-center overflow-hidden">
          {isRenamingHeaderTitle && currentSaveForHeader ? (
            <div className="flex items-center justify-center max-w-xs mx-auto">
              <input
                ref={headerRenameInputRef} type="text" value={headerRenameValue}
                onChange={(e) => setHeaderRenameValue(e.target.value)}
                onKeyDown={(e) => handleHeaderRenameKeyDownFromHook(e, currentQuickSaveId, renameSave, currentSaveForHeader?.name)} 
                onBlur={() => handleCancelHeaderRenameFromHook(currentSaveForHeader?.name)} 
                className="header-rename-input" maxLength={50}
              />
              <button onClick={() => handleConfirmHeaderRenameFromHook(currentQuickSaveId, renameSave, currentSaveForHeader?.name)} className="header-rename-button ml-1.5" title={UIText.saveGame}><Icons.Save className="w-4 h-4"/></button>
              <button onClick={() => handleCancelHeaderRenameFromHook(currentSaveForHeader?.name)} className="header-rename-button ml-1" title={UIText.cancelAction}><Icons.Close className="w-4 h-4"/></button>
            </div>
          ) : (
            <span 
              className={`truncate ${currentSaveForHeader ? 'cursor-pointer hover:underline' : ''}`} 
              title={currentSaveForHeader ? `${UIText.renameSave}: ${displayHeaderTitle}` : displayHeaderTitle}
              onClick={currentSaveForHeader ? () => handleHeaderTitleClick(currentSaveForHeader.name) : undefined}
            >
              {displayHeaderTitle === APP_TITLE_CN ? <AppTitleStyled /> : displayHeaderTitle}
            </span>
          )}
        </div>
        <div className="header-actions">
            <button onClick={handleQuickSaveGame} className="action-button" title={UIText.saveArchiveTooltip}><Icons.Save className="w-5 h-5"/></button>
            <button onClick={handleOpenLoadMenu} className="action-button" title={UIText.loadArchiveTooltip}><Icons.Load className="w-5 h-5"/></button>


            
            {/* Global Import Dropdown */}
            <Dropdown
                items={globalImportDropdownItems}
                dropdownAlign="right"
                menuWidthClass="w-64"
                triggerButton={(toggleDropdown, isOpen, buttonRef) => (
                    <button
                        ref={buttonRef}
                        onClick={toggleDropdown}
                        className="action-button"
                        title={UIText.globalImportsSectionTitle}
                        aria-haspopup="true"
                        aria-expanded={isOpen}
                    >
                        <Icons.ArrowUpTray className="w-5 h-5" />
                    </button>
                )}
            />
            {/* Hidden file inputs for global imports */}
            <input type="file" ref={importCharCardFileRef} onChange={handleImportSillyTavernCharCard} accept=".json,.png,.jsonc" className="hidden" multiple />
            <input type="file" ref={importUserRoleFileRef} onChange={handleImportUserRoleCharCard} accept=".json,.png,.jsonc" className="hidden" />
            <input type="file" ref={importWorldBookFileRef} onChange={handleImportSillyTavernWorldBook} accept=".json" className="hidden" multiple />


            <div className="relative">
              <button 
                id="restart-game-button-trigger"
                onClick={toggleRestartDropdown} 
                className="action-button" 
                title={UIText.restartGameTooltip}
                aria-haspopup="true"
                aria-expanded={isRestartDropdownOpen}
              >
                <Icons.ArrowPath className="w-5 h-5"/>
              </button>
              {isRestartDropdownOpen && (
                <div 
                  ref={restartDropdownRef}
                  className="absolute right-0 mt-2 w-52 bg-secondary-themed rounded-md shadow-themed-lg border border-themed z-50 py-1 animate-fadeIn"
                  role="menu"
                >
                  <button
                    onClick={() => selectRestartOption(handleRestartWithSummary)}
                    disabled={isLoading} 
                    className="w-full text-left px-3 py-1.5 text-sm text-primary-themed hover:bg-element-themed transition-colors duration-150 flex items-center"
                    role="menuitem"
                  >
                    {isLoading && dialogueLog.length > 0 && <Icons.ArrowPath className="w-4 h-4 mr-2 animate-spin" />}
                    {UIText.restartWithSummaryButton}
                  </button>
                  <button
                    onClick={() => selectRestartOption(handleRestartGameRequest)}
                    disabled={isLoading}
                    className="w-full text-left px-3 py-1.5 text-sm text-primary-themed hover:bg-element-themed transition-colors duration-150"
                    role="menuitem"
                  >
                    {UIText.restartGameTooltip} (当前设定)
                  </button>
                  <button
                    onClick={() => selectRestartOption(handleDefaultRestartGameRequest)}
                    disabled={isLoading}
                    className="w-full text-left px-3 py-1.5 text-sm text-primary-themed hover:bg-element-themed transition-colors duration-150"
                    role="menuitem"
                  >
                    {UIText.restartWithDefaultSettingsButton}
                  </button>
                </div>
              )}
            </div>

            {/* AI Provider Button */}
            <AIProviderButton className="mr-2" />

            <div className="relative">
                <button onClick={() => setIsStatusPanelOpen(!isStatusPanelOpen)} className="action-button" title={UIText.statusPanelToggle} aria-expanded={isStatusPanelOpen}>
                    <Icons.Profile className="w-5 h-5"/>
                </button>
                {canSpendPoints && (
                    <span className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-red-500 rounded-full border-2 border-[var(--bg-secondary)] pointer-events-none"></span>
                )}
            </div>
        </div>
      </header>
      
      <SidebarMenu
        isOpen={isSidebarOpen} toggleSidebar={handleSidebarClose}
        gameSaves={gameSaves}
        onSaveGame={(name) => saveGame(name, playerName, dialogueLog, currentSceneImageKeyword, playerStatus, convertDialogueLogToGeminiHistory(dialogueLog))}
        onLoadGame={loadGame} onDeleteSave={deleteSave} onRenameSave={renameSave} 
        onUpdateSave={(id) => updateSave(id, playerName, dialogueLog, currentSceneImageKeyword, playerStatus, convertDialogueLogToGeminiHistory(dialogueLog))}
        gameSettings={gameSettings} userPreferences={userPreferences} playerStatus={playerStatus}
        onGameSettingsChange={setGameSettings} onUserPreferencesChange={setUserPreferences}
        onPersistGameSettings={persistGameSettings} onPersistUserPreferences={persistUserPreferences}
        onRestartFromSave={restartFromSaveSettings} 
        onRestartWithSummary={handleRestartWithSummary}
        onDefaultRestartGameRequest={handleDefaultRestartGameRequest} 
        characterCardPresets={characterCardPresets} 
        userRolePresets={userRolePresets} aiStylePresets={aiStylePresets} 
        onSavePreset={savePreset} onLoadPreset={loadPreset} onDeletePreset={deletePreset}
        addNotification={addNotification} onRequestConfirmation={handleRequestConfirmation}
        onExportData={exportAllData} onImportData={importAllData} onResetAllSettingsToDefaults={() => handleResetAllSettingsToDefaults()}
        onAllocateAttributePoint={allocateAttributePoint} onAllocateSkillPoint={allocateSkillPoint}
        activeTab={sidebarActiveTab} onTabChange={setSidebarActiveTab}
        onInitializeGistBackup={handleInitializeGistBackup}
        onBackupToGist={() => handleBackupToGist(false)} 
        onRestoreFromGist={handleRestoreFromGist}
        isGistLoading={isGistLoading}
      />

      <main className="app-main">
        <div className="chat-interface-container">
          <ChatInterface
            dialogueLines={dialogueLog} onSendMessage={handleSendMessage}
            currentChoices={currentChoices} isLoading={isLoading} playerName={playerName}
            gameSettings={gameSettings}
            playerStatus={playerStatus} 
            onRegenerateResponse={handleRegenerateResponse} onDeleteLine={handleDeleteDialogueLine}
            isLoadingBackground={isLoadingBackground}
            // Editing props
            editingLineId={editingLineId}
            currentlyEditedContent={currentlyEditedContent}
            onStartEditLine={handleStartEditLine}
            onSaveEditedLine={handleSaveEditedLine}
            onCancelEditLine={handleCancelEditLine}
            onCurrentlyEditedContentChange={handleCurrentlyEditedContentChange}
          />
        </div>
      </main>

      {isStatusPanelOpen && (
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm z-20" onClick={() => setIsStatusPanelOpen(false)} aria-hidden="true" />
      )}
      <div ref={statusPanelRef} className={`fixed top-0 right-0 h-full z-30 transform transition-transform duration-300 ease-in-out w-80 md:w-96 ${isStatusPanelOpen ? 'translate-x-0' : 'translate-x-full'}`}>
        {isStatusPanelOpen && ( 
          <PersistentStatusDisplay
            status={playerStatus} playerName={playerName} className="h-full" 
            onSummarizeInventoryAndShowDetails={() => handleSummarizeInventoryAndShowDetails(dialogueLog, gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK)}
            onSummarizeLocationsAndShowDetails={() => handleSummarizeLocationsAndShowDetails(dialogueLog, gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK)}
            onSummarizeQuestsAndShowDetails={() => handleSummarizeQuestsAndShowDetails(dialogueLog, gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK)}
            onSummarizeCharactersAndShowDetails={() => handleSummarizeCharactersAndShowDetails(dialogueLog, gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK)}
            onSummarizeImportantEventsAndShowDetails={() => handleSummarizeImportantEventsAndShowDetails(dialogueLog, gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK)}
            isSummarizing={isSummarizing} enableBackdropBlur={gameSettings.enableBackdropBlur}
            onClosePanel={() => setIsStatusPanelOpen(false)}
            onAllocateAttribute={allocateAttributePoint} onAllocateSkill={allocateSkillPoint}
          />
        )}
      </div>
      <NotificationsContainer />
      <ConfirmationModal {...confirmationModalState} onCancel={closeConfirmationModal} enableBackdropBlur={gameSettings.enableBackdropBlur} />
      
      {activeDetailModal === 'inventory' && <InventoryModal isOpen={true} items={playerStatus.inventory} onClose={closeDetailModal} enableBackdropBlur={gameSettings.enableBackdropBlur} />}
      {activeDetailModal === 'locations' && <LocationsModal isOpen={true} locations={playerStatus.visitedLocations} onClose={closeDetailModal} enableBackdropBlur={gameSettings.enableBackdropBlur} />}
      {activeDetailModal === 'quests' && <QuestsModal isOpen={true} quests={playerStatus.quests} onClose={closeDetailModal} enableBackdropBlur={gameSettings.enableBackdropBlur} />}
      {activeDetailModal === 'characters' && <CharactersModal isOpen={true} characters={playerStatus.characterProfiles} onClose={closeDetailModal} enableBackdropBlur={gameSettings.enableBackdropBlur} />}
      {activeDetailModal === 'importantEvents' && <ImportantEventsModal isOpen={true} events={playerStatus.importantEvents} onClose={closeDetailModal} enableBackdropBlur={gameSettings.enableBackdropBlur} />}


    </div>
  );
};
